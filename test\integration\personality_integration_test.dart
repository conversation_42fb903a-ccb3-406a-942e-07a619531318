import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:toxic_chat/main.dart';
import 'package:toxic_chat/models/personality_mode.dart';
import 'package:toxic_chat/providers/theme_provider.dart';
import 'package:toxic_chat/screens/chat_screen.dart';
import 'package:toxic_chat/screens/settings_screen.dart';
import 'package:toxic_chat/services/personality_service.dart';

void main() {
  group('Personality Integration Tests', () {
    testWidgets('can open settings and change personality', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const ToxicChatApp(),
        ),
      );

      // Open settings screen
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();

      // Verify settings screen is open
      expect(find.byType(SettingsScreen), findsOneWidget);

      // Tap to open personality selector
      await tester.tap(find.text('Personality'));
      await tester.pumpAndSettle();

      // Change personality to Sarcastic
      await tester.tap(find.text(PersonalityMode.sarcastic.displayName));
      await tester.pumpAndSettle();

      // Verify personality changed
      final personalityService = PersonalityService();
      await personalityService.loadConfiguration();
      expect(
        personalityService.currentConfig.currentPersonality,
        PersonalityMode.sarcastic,
      );
    });
  });
}
