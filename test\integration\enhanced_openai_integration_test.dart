import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/openai_service.dart';
import '../../lib/models/personality_mode.dart';

void main() {
  group('Enhanced OpenAI Integration Tests', () {
    late OpenAIService openAIService;

    setUp(() {
      openAIService = OpenAIService(apiKey: 'test-key');
    });

    tearDown(() {
      openAIService.dispose();
    });

    test('should build system prompts for different personalities and languages', () {
      // Test different personality and language combinations
      final testCases = [
        (PersonalityMode.toxic, 'english'),
        (PersonalityMode.toxic, 'spanish'),
        (PersonalityMode.witch, 'english'),
        (PersonalityMode.witch, 'french'),
        (PersonalityMode.jealous, 'portuguese'),
        (PersonalityMode.dramatic, 'italian'),
        (PersonalityMode.sarcastic, 'english'),
      ];

      for (final testCase in testCases) {
        final personality = testCase.$1;
        final language = testCase.$2;

        // This should not throw an exception
        expect(() {
          openAIService.buildEmotionalResponsePrompt(
            'Test message',
            personality,
            language,
          );
        }, returnsNormally);
      }
    });

    test('should create localized response templates for all personalities', () {
      final personalities = PersonalityMode.values;
      final languages = ['english', 'spanish', 'french', 'portuguese', 'italian'];

      for (final personality in personalities) {
        for (final language in languages) {
          final templates = openAIService.createLocalizedResponseTemplates(
            personality,
            language,
          );

          // Verify all required template keys exist
          expect(templates.keys, contains('greeting'));
          expect(templates.keys, contains('insult_response'));
          expect(templates.keys, contains('question_response'));
          expect(templates.keys, contains('goodbye'));

          // Verify templates are not empty
          for (final template in templates.values) {
            expect(template, isNotEmpty);
          }
        }
      }
    });

    test('should handle language normalization correctly', () {
      final testCases = [
        ('en', 'english'),
        ('eng', 'english'),
        ('english', 'english'),
        ('es', 'spanish'),
        ('spa', 'spanish'),
        ('spanish', 'spanish'),
        ('fr', 'french'),
        ('fra', 'french'),
        ('french', 'french'),
        ('pt', 'portuguese'),
        ('por', 'portuguese'),
        ('portuguese', 'portuguese'),
        ('it', 'italian'),
        ('ita', 'italian'),
        ('italian', 'italian'),
        ('unknown_language', 'english'), // Should default to English
      ];

      for (final testCase in testCases) {
        final input = testCase.$1;
        final expected = testCase.$2;

        final templates = openAIService.createLocalizedResponseTemplates(
          PersonalityMode.toxic,
          input,
        );

        // Verify that templates are created (indicating proper normalization)
        expect(templates, isNotEmpty);
        expect(templates.keys, contains('greeting'));
      }
    });

    test('should generate different prompts for different personalities', () {
      final userMessage = 'You are stupid!';
      final language = 'english';

      final toxicPrompt = openAIService.buildEmotionalResponsePrompt(
        userMessage,
        PersonalityMode.toxic,
        language,
      );

      final witchPrompt = openAIService.buildEmotionalResponsePrompt(
        userMessage,
        PersonalityMode.witch,
        language,
      );

      final sarcasticPrompt = openAIService.buildEmotionalResponsePrompt(
        userMessage,
        PersonalityMode.sarcastic,
        language,
      );

      // Verify prompts are different
      expect(toxicPrompt, isNot(equals(witchPrompt)));
      expect(toxicPrompt, isNot(equals(sarcasticPrompt)));
      expect(witchPrompt, isNot(equals(sarcasticPrompt)));

      // Verify prompts contain personality-specific content
      expect(toxicPrompt, contains('toxic'));
      expect(witchPrompt, contains('witch'));
      expect(sarcasticPrompt, contains('sarcastic'));
    });

    test('should generate different prompts for different languages', () {
      final userMessage = 'Hello';
      final personality = PersonalityMode.toxic;

      final englishPrompt = openAIService.buildEmotionalResponsePrompt(
        userMessage,
        personality,
        'english',
      );

      final spanishPrompt = openAIService.buildEmotionalResponsePrompt(
        userMessage,
        personality,
        'spanish',
      );

      final frenchPrompt = openAIService.buildEmotionalResponsePrompt(
        userMessage,
        personality,
        'french',
      );

      // Verify prompts are different
      expect(englishPrompt, isNot(equals(spanishPrompt)));
      expect(englishPrompt, isNot(equals(frenchPrompt)));
      expect(spanishPrompt, isNot(equals(frenchPrompt)));

      // Verify prompts contain language-specific content
      expect(spanishPrompt, contains('tóxico'));
      expect(frenchPrompt, contains('toxique'));
    });

    test('should create personality-specific response templates', () {
      final language = 'english';

      // Test toxic personality templates
      final toxicTemplates = openAIService.createLocalizedResponseTemplates(
        PersonalityMode.toxic,
        language,
      );
      expect(toxicTemplates['greeting'], contains('NOW you talk'));

      // Test witch personality templates
      final witchTemplates = openAIService.createLocalizedResponseTemplates(
        PersonalityMode.witch,
        language,
      );
      expect(witchTemplates['greeting'], contains('stars told me'));

      // Test jealous personality templates
      final jealousTemplates = openAIService.createLocalizedResponseTemplates(
        PersonalityMode.jealous,
        language,
      );
      expect(jealousTemplates['greeting'], contains('Who were you talking'));

      // Test dramatic personality templates
      final dramaticTemplates = openAIService.createLocalizedResponseTemplates(
        PersonalityMode.dramatic,
        language,
      );
      expect(dramaticTemplates['greeting'], contains('FINALLY!!!'));

      // Test sarcastic personality templates
      final sarcasticTemplates = openAIService.createLocalizedResponseTemplates(
        PersonalityMode.sarcastic,
        language,
      );
      expect(sarcasticTemplates['greeting'], contains('what a surprise'));
    });
  });
}