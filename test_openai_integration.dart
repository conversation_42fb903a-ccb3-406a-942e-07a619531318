// Simple test file to verify OpenAI integration
// Run this with: dart run test_openai_integration.dart

import 'lib/services/openai_service.dart';
import 'lib/config/app_config.dart';

void main() async {
  print('Testing OpenAI integration...');
  
  // Check if API key is configured
  if (!AppConfig.isConfigured) {
    print('❌ API key not configured! Please set your OpenAI API key in lib/config/app_config.dart');
    return;
  }
  
  print('✅ API key configured');
  
  // Create OpenAI service
  final openAiService = OpenAIService();
  
  try {
    // Test message
    final messages = [
      {'role': 'user', 'content': 'Hey, how are you?'}
    ];
    
    print('🤖 Sending test message to OpenAI...');
    
    final response = await openAiService.sendChatRequest(messages);
    
    print('✅ Response received:');
    print('💬 AI: $response');
    print('🎉 OpenAI integration working correctly!');
    
  } catch (e) {
    print('❌ Error: $e');
    print('💡 Common issues:');
    print('   - Invalid API key');
    print('   - Network connection');
    print('   - OpenAI service outage');
    print('   - Rate limits exceeded');
  } finally {
    openAiService.dispose();
  }
}
