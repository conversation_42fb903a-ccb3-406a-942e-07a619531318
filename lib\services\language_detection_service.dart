/// Service for detecting the language of text input
/// Supports Spanish, English, French, Portuguese, and Italian
class LanguageDetectionService {
  // Common words for each supported language
  static const Map<String, List<String>> _languageKeywords = {
    'es': [
      // Spanish common words
      'el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para',
      'hola', 'como', 'estas', 'que', 'donde', 'cuando', 'porque', 'si', 'pero', 'muy', 'bien', 'mal', 'bueno', 'malo',
      'gracias', 'por favor', 'disculpa', 'perdón', 'sí', 'también', 'ahora', 'aquí', 'allí', 'todo', 'nada', 'algo'
    ],
    'en': [
      // English common words
      'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at',
      'hello', 'how', 'are', 'what', 'where', 'when', 'why', 'if', 'but', 'very', 'good', 'bad', 'well', 'yes', 'no',
      'thanks', 'please', 'sorry', 'excuse', 'also', 'now', 'here', 'there', 'all', 'nothing', 'something'
    ],
    'fr': [
      // French common words
      'le', 'de', 'et', 'à', 'un', 'il', 'être', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se',
      'bonjour', 'comment', 'allez', 'vous', 'quoi', 'où', 'quand', 'pourquoi', 'si', 'mais', 'très', 'bien', 'mal', 'bon', 'mauvais',
      'merci', 'excusez', 'pardon', 'oui', 'non', 'aussi', 'maintenant', 'ici', 'là', 'tout', 'rien', 'quelque', 'chose', 'tu', 'me',
      'manques', 'beaucoup', 'je', 'suis', 'triste', 'est', 'du', 'la', 'les', 'des', 'au', 'aux', 'cette', 'ces', 'mon', 'ma', 'mes'
    ],
    'pt': [
      // Portuguese common words
      'o', 'de', 'e', 'do', 'a', 'em', 'um', 'para', 'é', 'com', 'não', 'uma', 'os', 'no', 'se', 'na', 'por', 'mais', 'as', 'dos',
      'olá', 'como', 'está', 'você', 'que', 'onde', 'quando', 'porque', 'se', 'mas', 'muito', 'bem', 'mal', 'bom', 'ruim',
      'obrigado', 'por favor', 'desculpa', 'perdão', 'sim', 'não', 'também', 'agora', 'aqui', 'ali', 'tudo', 'nada', 'algo'
    ],
    'it': [
      // Italian common words
      'il', 'di', 'e', 'la', 'a', 'del', 'un', 'in', 'da', 'per', 'con', 'non', 'una', 'su', 'le', 'si', 'che', 'come', 'o', 'se',
      'ciao', 'come', 'stai', 'cosa', 'dove', 'quando', 'perché', 'se', 'ma', 'molto', 'bene', 'male', 'buono', 'cattivo',
      'grazie', 'per favore', 'scusa', 'perdono', 'sì', 'no', 'anche', 'ora', 'qui', 'lì', 'tutto', 'niente', 'qualcosa'
    ],
  };

  // Character patterns for fallback detection
  static final Map<String, RegExp> _characterPatterns = {
    'es': RegExp(r'[ñáéíóúü]', caseSensitive: false),
    'fr': RegExp(r'[àâäéèêëïîôöùûüÿç]', caseSensitive: false),
    'pt': RegExp(r'[ãâáàçéêíôõóúü]', caseSensitive: false),
    'it': RegExp(r'[àèéìíîòóù]', caseSensitive: false),
  };

  // Language-specific common patterns and phrases
  static const Map<String, List<String>> _languagePhrases = {
    'es': ['señorita', 'maría', 'josé', 'niño', 'niña', 'año', 'años'],
    'fr': ['français', 'café', 'très', 'délicieux', 'beaucoup', 'aujourd\'hui'],
    'pt': ['joão', 'maria', 'são', 'irmãos', 'atenção', 'compreensão', 'ótimo'],
    'it': ['grazie', 'mille', 'gentilezza', 'buongiorno', 'bella', 'giornata'],
    'en': ['hello', 'today', 'wonderful', 'morning', 'thanks', 'please'],
  };

  /// Detects the language of the given text
  /// Returns language code (es, en, fr, pt, it) or 'en' as default
  Future<String> detectLanguage(String text) async {
    if (text.trim().isEmpty) {
      return 'en'; // Default to English for empty text
    }

    final cleanText = _cleanText(text);
    final words = _extractWords(cleanText);
    
    if (words.isEmpty) {
      return 'en';
    }

    // Primary detection: keyword matching
    final keywordScores = _calculateKeywordScores(words);
    
    // Secondary detection: character pattern analysis
    final characterScores = _calculateCharacterScores(text);
    
    // Tertiary detection: phrase pattern matching
    final phraseScores = _calculatePhraseScores(cleanText);
    
    // Combine scores with weights (50% keywords, 30% characters, 20% phrases)
    final combinedScores = <String, double>{};
    for (final lang in _languageKeywords.keys) {
      final keywordScore = keywordScores[lang] ?? 0.0;
      final characterScore = characterScores[lang] ?? 0.0;
      final phraseScore = phraseScores[lang] ?? 0.0;
      combinedScores[lang] = (keywordScore * 0.5) + (characterScore * 0.3) + (phraseScore * 0.2);
    }
    
    // Find the language with highest score
    String detectedLanguage = 'en';
    double maxScore = combinedScores['en'] ?? 0.0;
    
    for (final entry in combinedScores.entries) {
      if (entry.value > maxScore) {
        maxScore = entry.value;
        detectedLanguage = entry.key;
      }
    }
    
    // Require minimum confidence threshold
    if (maxScore < 0.1) {
      return 'en'; // Default to English if confidence is too low
    }
    
    return detectedLanguage;
  }

  /// Checks if a language is supported by the detection service
  bool isLanguageSupported(String languageCode) {
    return _languageKeywords.containsKey(languageCode);
  }

  /// Returns the human-readable name for a language code
  String getLanguageName(String languageCode) {
    const languageNames = {
      'es': 'Spanish',
      'en': 'English',
      'fr': 'French',
      'pt': 'Portuguese',
      'it': 'Italian',
    };
    return languageNames[languageCode] ?? 'Unknown';
  }

  /// Returns all supported language codes
  List<String> getSupportedLanguages() {
    return _languageKeywords.keys.toList();
  }

  // Private helper methods

  String _cleanText(String text) {
    return text.toLowerCase()
        .replaceAll(RegExp(r'[^\w\sáéíóúüñàâäéèêëïîôöùûüÿçãâáàçéêíôõóúüàèéìíîòóù]'), ' ')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  List<String> _extractWords(String text) {
    return text.split(' ')
        .where((word) => word.length >= 2)
        .toList();
  }

  Map<String, double> _calculateKeywordScores(List<String> words) {
    final scores = <String, double>{};
    
    for (final lang in _languageKeywords.keys) {
      final keywords = _languageKeywords[lang]!;
      int matches = 0;
      
      for (final word in words) {
        if (keywords.contains(word)) {
          matches++;
        }
      }
      
      // Calculate score as percentage of matched words
      scores[lang] = words.isNotEmpty ? matches / words.length : 0.0;
    }
    
    return scores;
  }

  Map<String, double> _calculateCharacterScores(String text) {
    final scores = <String, double>{};
    
    for (final entry in _characterPatterns.entries) {
      final lang = entry.key;
      final pattern = entry.value;
      final matches = pattern.allMatches(text).length;
      
      // Calculate score based on character frequency, with higher weight for special chars
      scores[lang] = text.isNotEmpty ? (matches / text.length) * 10 : 0.0;
    }
    
    // English doesn't have special characters, so give it a base score
    scores['en'] = 0.1;
    
    return scores;
  }

  Map<String, double> _calculatePhraseScores(String text) {
    final scores = <String, double>{};
    
    for (final entry in _languagePhrases.entries) {
      final lang = entry.key;
      final phrases = entry.value;
      int matches = 0;
      
      for (final phrase in phrases) {
        if (text.contains(phrase)) {
          matches++;
        }
      }
      
      // Calculate score based on phrase matches
      scores[lang] = phrases.isNotEmpty ? matches / phrases.length : 0.0;
    }
    
    return scores;
  }
}