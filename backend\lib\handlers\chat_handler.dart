import 'dart:convert';
import 'package:shelf/shelf.dart';
import '../services/openai_service.dart';

/// <PERSON><PERSON> for the /chat POST endpoint
class ChatHandler {
  final OpenAIService _openAiService;

  ChatHandler({OpenAIService? openAiService}) 
      : _openAiService = openAiService ?? OpenAIService();

  /// Handle chat requests
  Future<Response> handleChatRequest(Request request) async {
    try {
      // Validate HTTP method
      if (request.method != 'POST') {
        return _errorResponse(405, 'Method not allowed. Use POST.');
      }

      // Validate content type
      final contentType = request.headers['content-type'];
      if (contentType == null || !contentType.contains('application/json')) {
        return _errorResponse(400, 'Content-Type must be application/json');
      }

      // Parse request body
      final requestBody = await request.readAsString();
      if (requestBody.isEmpty) {
        return _errorResponse(400, 'Request body cannot be empty');
      }

      Map<String, dynamic> requestData;
      try {
        requestData = json.decode(requestBody) as Map<String, dynamic>;
      } catch (e) {
        return _errorResponse(400, 'Invalid JSON format');
      }

      // Validate and process message history
      final messages = await _validateAndProcessMessages(requestData);
      if (messages == null) {
        return _errorResponse(400, 'Invalid messages format. Expected array of message objects.');
      }

      // Call OpenAI service
      final aiResponse = await _openAiService.sendChatRequest(messages);

      // Return formatted response
      return _formatResponse(aiResponse);

    } catch (e) {
      // Handle OpenAI service errors
      if (e is OpenAIException) {
        return _errorResponse(503, 'AI service error: ${e.message}');
      }
      
      // Handle unexpected errors
      return _errorResponse(500, 'Internal server error');
    }
  }

  /// Validate and process incoming messages
  Future<List<Map<String, dynamic>>?> _validateAndProcessMessages(
      Map<String, dynamic> requestData) async {
    
    final messagesData = requestData['messages'];
    if (messagesData == null) {
      return null;
    }

    if (messagesData is! List) {
      return null;
    }

    final List<Map<String, dynamic>> processedMessages = [];

    for (final messageData in messagesData) {
      if (messageData is! Map<String, dynamic>) {
        return null;
      }

      // Validate required fields
      final text = messageData['text'];
      final isUser = messageData['isUser'];

      if (text == null || text is! String || text.trim().isEmpty) {
        return null;
      }

      if (isUser == null || isUser is! bool) {
        return null;
      }

      // Convert to OpenAI format
      final openAiMessage = {
        'role': isUser ? 'user' : 'assistant',
        'content': text.trim(),
      };

      processedMessages.add(openAiMessage);
    }

    // Ensure we have at least one user message
    if (processedMessages.isEmpty) {
      return null;
    }

    // Validate that the last message is from user (for proper conversation flow)
    final lastMessage = processedMessages.last;
    if (lastMessage['role'] != 'user') {
      return null;
    }

    return processedMessages;
  }

  /// Format successful AI response
  Response _formatResponse(String aiResponse) {
    final responseData = {
      'message': aiResponse,
      'success': true,
      'timestamp': DateTime.now().toIso8601String(),
    };

    return Response.ok(
      json.encode(responseData),
      headers: {'Content-Type': 'application/json'},
    );
  }

  /// Format error response
  Response _errorResponse(int statusCode, String errorMessage) {
    final responseData = {
      'success': false,
      'error': errorMessage,
      'timestamp': DateTime.now().toIso8601String(),
    };

    return Response(
      statusCode,
      body: json.encode(responseData),
      headers: {'Content-Type': 'application/json'},
    );
  }

  /// Dispose of resources
  void dispose() {
    _openAiService.dispose();
  }
}