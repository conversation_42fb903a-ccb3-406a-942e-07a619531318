import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:http/testing.dart';
import 'package:provider/provider.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/providers/theme_provider.dart';
import 'package:toxic_chat/services/chat_service.dart';
import 'package:toxic_chat/services/openai_service.dart';

void main() {
  group('Error Handling Integration Tests', () {
    late MockClient mockClient;
    late ChatService chatService;
    late OpenAIService openAIService;

    setUp(() {
      mockClient = MockClient((request) async {
        return http.Response(
          '{"choices": [{"message": {"content": "Test response"}}]}',
          200,
        );
      });
      openAIService = OpenAIService(httpClient: mockClient);
      chatService = ChatService(openAiService: openAIService);
    });

    tearDown(() {
      chatService.dispose();
    });

    group('Network Error Scenarios', () {
      testWidgets('should handle network connection timeout', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          throw http.ClientException('Connection timeout');
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.network,
                )
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });

      testWidgets('should handle DNS resolution failure', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          throw const SocketException('Failed host lookup');
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.network,
                )
                .having((e) => e.message, 'message', contains('connection'))
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });

      testWidgets('should handle connection refused', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          throw const SocketException('Connection refused');
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.network,
                )
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });
    });

    group('HTTP Error Status Scenarios', () {
      testWidgets('should handle 400 Bad Request', (WidgetTester tester) async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'error': {'message': 'Invalid message format'},
            }),
            400,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having((e) => e.statusCode, 'statusCode', 400)
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.validation,
                )
                .having((e) => e.isRetryable, 'isRetryable', false),
          ),
        );
      });

      testWidgets('should handle 401 Unauthorized', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'error': {'message': 'Invalid API key'},
            }),
            401,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having((e) => e.statusCode, 'statusCode', 401)
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.authentication,
                )
                .having((e) => e.isRetryable, 'isRetryable', false),
          ),
        );
      });

      testWidgets('should handle 429 Rate Limit', (WidgetTester tester) async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'error': {'message': 'Rate limit exceeded. Please wait.'},
            }),
            429,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having((e) => e.statusCode, 'statusCode', 429)
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.rateLimit,
                )
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });

      testWidgets('should handle 500 Internal Server Error', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'error': {'message': 'Internal server error'},
            }),
            500,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having((e) => e.statusCode, 'statusCode', 500)
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.serverError,
                )
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });

      testWidgets('should handle 503 Service Unavailable', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'error': {'message': 'Service temporarily unavailable'},
            }),
            503,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having((e) => e.statusCode, 'statusCode', 503)
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.serverError,
                )
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });
    });

    group('Response Format Error Scenarios', () {
      testWidgets('should handle malformed JSON response', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response('This is not valid JSON', 200);
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.serverError,
                )
                .having(
                  (e) => e.message,
                  'message',
                  contains('Invalid response format'),
                )
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });

      testWidgets('should handle missing success field', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'choices': [
                {
                  'message': {'content': 'Test response'},
                },
              ],
            }),
            200,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.serverError,
                )
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });

      testWidgets('should handle empty message content', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'choices': [
                {
                  'message': {'content': ''},
                },
              ],
            }),
            200,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.serverError,
                )
                .having((e) => e.message, 'message', contains('Empty response'))
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });

      testWidgets('should handle null message content', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'choices': [
                {
                  'message': {'content': null},
                },
              ],
            }),
            200,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
              role: 'user',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.serverError,
                )
                .having((e) => e.message, 'message', contains('Empty response'))
                .having((e) => e.isRetryable, 'isRetryable', true),
          ),
        );
      });
    });

    group('Service Availability Error Scenarios', () {
      testWidgets('should handle health check failure', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          if (request.url.path.contains('/health')) {
            return http.Response('Service Unavailable', 503);
          }
          return http.Response(
            '{"choices": [{"message": {"content": "Test"}}]}',
            200,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act
        final isAvailable = await chatService.isServiceAvailable();

        // Assert
        expect(isAvailable, false);
      });

      testWidgets('should handle health check network error', (
        WidgetTester tester,
      ) async {
        // Arrange
        mockClient = MockClient((request) async {
          if (request.url.path.contains('/health')) {
            throw const SocketException('Connection failed');
          }
          return http.Response(
            '{"choices": [{"message": {"content": "Test"}}]}',
            200,
          );
        });
        openAIService = OpenAIService(httpClient: mockClient);
        chatService = ChatService(openAiService: openAIService);

        // Act
        final isAvailable = await chatService.isServiceAvailable();

        // Assert
        expect(isAvailable, false);
      });
    });

    group('Validation Error Scenarios', () {
      testWidgets('should handle empty message list', (
        WidgetTester tester,
      ) async {
        // Act & Assert
        expect(
          () => chatService.sendMessage([]),
          throwsA(
            isA<ChatServiceException>()
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.validation,
                )
                .having((e) => e.message, 'message', contains('empty'))
                .having((e) => e.isRetryable, 'isRetryable', false),
          ),
        );
      });

      testWidgets('should handle last message not from user', (
        WidgetTester tester,
      ) async {
        // Act & Assert
        expect(
          () => chatService.sendMessage([
            Message(
              text: 'Hello',
              isUser: false,
              timestamp: DateTime.now(),
              role: 'assistant',
            ),
          ]),
          throwsA(
            isA<ChatServiceException>()
                .having(
                  (e) => e.errorType,
                  'errorType',
                  ChatServiceErrorType.validation,
                )
                .having(
                  (e) => e.message,
                  'message',
                  contains('Last message must be from user'),
                )
                .having((e) => e.isRetryable, 'isRetryable', false),
          ),
        );
      });
    });

    group('Error Message Generation Tests', () {
      test(
        'should generate appropriate error messages for different error types',
        () {
          // Network error
          const networkError = ChatServiceException(
            'Connection failed',
            null,
            ChatServiceErrorType.network,
            true,
          );
          expect(networkError.errorType, ChatServiceErrorType.network);
          expect(networkError.isRetryable, true);

          // Authentication error
          const authError = ChatServiceException(
            'Invalid API key',
            401,
            ChatServiceErrorType.authentication,
            false,
          );
          expect(authError.errorType, ChatServiceErrorType.authentication);
          expect(authError.isRetryable, false);

          // Rate limit error
          const rateLimitError = ChatServiceException(
            'Rate limit exceeded',
            429,
            ChatServiceErrorType.rateLimit,
            true,
          );
          expect(rateLimitError.errorType, ChatServiceErrorType.rateLimit);
          expect(rateLimitError.isRetryable, true);

          // Server error
          const serverError = ChatServiceException(
            'Internal server error',
            500,
            ChatServiceErrorType.serverError,
            true,
          );
          expect(serverError.errorType, ChatServiceErrorType.serverError);
          expect(serverError.isRetryable, true);

          // Validation error
          const validationError = ChatServiceException(
            'Invalid request',
            400,
            ChatServiceErrorType.validation,
            false,
          );
          expect(validationError.errorType, ChatServiceErrorType.validation);
          expect(validationError.isRetryable, false);
        },
      );
    });
  });
}
