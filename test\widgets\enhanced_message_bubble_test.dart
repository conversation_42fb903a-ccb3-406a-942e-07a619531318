import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/widgets/message_bubble.dart';

void main() {
  group('Enhanced MessageBubble Tests', () {
    late Message userMessage;
    late Message aiMessage;

    setUp(() {
      userMessage = Message(
        text: 'Hello, this is a user message!',
        isUser: true,
        timestamp: DateTime.now(),
        role: 'user',
      );

      aiMessage = Message(
        text: 'Hello! This is an AI response.',
        isUser: false,
        timestamp: DateTime.now().subtract(const Duration(minutes: 1)),
        role: 'assistant',
      );
    });

    testWidgets('should render user message with right alignment', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: MessageBubble(message: userMessage)),
        ),
      );

      expect(find.text('Hello, this is a user message!'), findsOneWidget);
      expect(find.text('Just now'), findsOneWidget);
    });

    testWidgets('should render AI message with left alignment', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: MessageBubble(message: aiMessage)),
        ),
      );

      expect(find.text('Hello! This is an AI response.'), findsOneWidget);
      expect(find.text('1m ago'), findsOneWidget);
    });

    testWidgets('should hide timestamp when showTimestamp is false', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: userMessage, showTimestamp: false),
          ),
        ),
      );

      expect(find.text('Hello, this is a user message!'), findsOneWidget);
      expect(find.text('Just now'), findsNothing);
    });
  });

  group('Message Model Enhancement Tests', () {
    test('should format timestamp correctly', () {
      final now = DateTime.now();
      final message1 = Message(text: 'Test', isUser: true, timestamp: now);
      expect(message1.getFormattedTimestamp(), equals('Just now'));

      final message2 = Message(
        text: 'Test',
        isUser: true,
        timestamp: now.subtract(const Duration(minutes: 5)),
      );
      expect(message2.getFormattedTimestamp(), equals('5m ago'));

      final message3 = Message(
        text: 'Test',
        isUser: true,
        timestamp: now.subtract(const Duration(hours: 2)),
      );
      expect(message3.getFormattedTimestamp(), equals('2h ago'));
    });
  });
}
