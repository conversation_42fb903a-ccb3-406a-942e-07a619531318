import 'package:flutter/material.dart';
import '../models/personality_mode.dart';
import '../services/personality_service.dart';
import '../utils/responsive_utils.dart';

/// Widget for selecting and switching personality modes
class PersonalitySelector extends StatefulWidget {
  final PersonalityMode currentPersonality;
  final Function(PersonalityMode) onPersonalityChanged;
  final bool showAsBottomSheet;
  final bool showDescription;

  const PersonalitySelector({
    super.key,
    required this.currentPersonality,
    required this.onPersonalityChanged,
    this.showAsBottomSheet = false,
    this.showDescription = true,
  });

  @override
  State<PersonalitySelector> createState() => _PersonalitySelectorState();
}

class _PersonalitySelectorState extends State<PersonalitySelector>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  final PersonalityService _personalityService = PersonalityService();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Get icon for personality mode
  IconData _getPersonalityIcon(PersonalityMode personality) {
    switch (personality) {
      case PersonalityMode.toxic:
        return Icons.warning_amber_rounded;
      case PersonalityMode.witch:
        return Icons.auto_fix_high_rounded;
      case PersonalityMode.jealous:
        return Icons.favorite_border_rounded;
      case PersonalityMode.dramatic:
        return Icons.theater_comedy_rounded;
      case PersonalityMode.sarcastic:
        return Icons.sentiment_very_dissatisfied_rounded;
    }
  }

  /// Get color for personality mode
  Color _getPersonalityColor(PersonalityMode personality) {
    switch (personality) {
      case PersonalityMode.toxic:
        return Colors.red.shade600;
      case PersonalityMode.witch:
        return Colors.purple.shade600;
      case PersonalityMode.jealous:
        return Colors.pink.shade600;
      case PersonalityMode.dramatic:
        return Colors.orange.shade600;
      case PersonalityMode.sarcastic:
        return Colors.indigo.shade600;
    }
  }

  /// Handle personality selection
  Future<void> _selectPersonality(PersonalityMode personality) async {
    if (personality == widget.currentPersonality) return;

    // Update personality service
    await _personalityService.changePersonality(personality);

    // Notify parent widget
    widget.onPersonalityChanged(personality);

    // Show feedback
    if (mounted) {
      _showPersonalityChangedFeedback(personality);
    }
  }

  /// Show feedback when personality changes
  void _showPersonalityChangedFeedback(PersonalityMode personality) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getPersonalityIcon(personality),
              color: Colors.white,
              size: 20.0,
            ),
            const SizedBox(width: 12.0),
            Text(
              'Switched to ${personality.displayName} personality',
              style: const TextStyle(
                fontSize: 14.0,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: _getPersonalityColor(personality),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showAsBottomSheet) {
      return _buildBottomSheetContent(context);
    } else {
      return _buildInlineContent(context);
    }
  }

  /// Build content for bottom sheet presentation
  Widget _buildBottomSheetContent(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24.0)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12.0),
            width: 40.0,
            height: 4.0,
            decoration: BoxDecoration(
              color: colorScheme.outline.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2.0),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Row(
              children: [
                Icon(
                  Icons.psychology_rounded,
                  color: colorScheme.primary,
                  size: 28.0,
                ),
                const SizedBox(width: 12.0),
                Text(
                  'Choose Personality',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),

          // Personality options
          Flexible(child: _buildPersonalityGrid(context)),

          // Bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom + 16.0),
        ],
      ),
    );
  }

  /// Build content for inline presentation
  Widget _buildInlineContent(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _buildPersonalityGrid(context),
        );
      },
    );
  }

  /// Build the personality selection grid
  Widget _buildPersonalityGrid(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isMobile = ResponsiveUtils.isMobile(context);
    final personalities = PersonalityMode.values;

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveUtils.getResponsiveSpacing(
          context,
          mobile: 16.0,
          tablet: 24.0,
          desktop: 32.0,
        ),
      ),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: isMobile ? 2 : 3,
          crossAxisSpacing: 12.0,
          mainAxisSpacing: 12.0,
          childAspectRatio: isMobile ? 1.1 : 1.2,
        ),
        itemCount: personalities.length,
        itemBuilder: (context, index) {
          final personality = personalities[index];
          final isSelected = personality == widget.currentPersonality;
          final personalityColor = _getPersonalityColor(personality);

          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOutCubic,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _selectPersonality(personality),
                borderRadius: BorderRadius.circular(16.0),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.0),
                    border: Border.all(
                      color: isSelected
                          ? personalityColor
                          : colorScheme.outline.withValues(alpha: 0.2),
                      width: isSelected ? 2.5 : 1.0,
                    ),
                    gradient: isSelected
                        ? LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              personalityColor.withValues(alpha: 0.1),
                              personalityColor.withValues(alpha: 0.05),
                            ],
                          )
                        : null,
                    color: isSelected ? null : colorScheme.surface,
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: personalityColor.withValues(alpha: 0.3),
                              blurRadius: 12.0,
                              spreadRadius: 2.0,
                            ),
                          ]
                        : [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 8.0,
                              offset: const Offset(0, 2),
                            ),
                          ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Personality icon
                          Container(
                            padding: const EdgeInsets.all(12.0),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected
                                  ? personalityColor
                                  : personalityColor.withValues(alpha: 0.1),
                            ),
                            child: Icon(
                              _getPersonalityIcon(personality),
                              color: isSelected
                                  ? Colors.white
                                  : personalityColor,
                              size: ResponsiveUtils.getResponsiveIconSize(
                                context,
                                28.0,
                              ),
                            ),
                          ),

                          const SizedBox(height: 12.0),

                          // Personality name
                          Text(
                            personality.displayName,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.w600,
                              color: isSelected
                                  ? personalityColor
                                  : colorScheme.onSurface,
                              fontSize: ResponsiveUtils.getResponsiveFontSize(
                                context,
                                15.0,
                              ),
                            ),
                            textAlign: TextAlign.center,
                          ),

                          // Description (if enabled and space allows)
                          if (widget.showDescription) ...[
                            const SizedBox(height: 6.0),
                            Text(
                              personality.description,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                                fontSize: ResponsiveUtils.getResponsiveFontSize(
                                  context,
                                  12.0,
                                ),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],

                          // Selection indicator
                          if (isSelected) ...[
                            const SizedBox(height: 8.0),
                            Icon(
                              Icons.check_circle,
                              color: personalityColor,
                              size: 20.0,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Helper function to show personality selector as bottom sheet
Future<void> showPersonalitySelector({
  required BuildContext context,
  required PersonalityMode currentPersonality,
  required Function(PersonalityMode) onPersonalityChanged,
}) {
  return showModalBottomSheet<void>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return PersonalitySelector(
        currentPersonality: currentPersonality,
        onPersonalityChanged: onPersonalityChanged,
        showAsBottomSheet: true,
      );
    },
  );
}
