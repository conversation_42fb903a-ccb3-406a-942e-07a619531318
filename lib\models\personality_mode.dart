/// Enum representing different personality modes for the AI chat responses
enum PersonalityMode {
  toxic,
  witch,
  jealous,
  dramatic,
  sarcastic,
}

/// Extension to provide human-readable names and descriptions for personality modes
extension PersonalityModeExtension on PersonalityMode {
  /// Returns the display name for the personality mode
  String get displayName {
    switch (this) {
      case PersonalityMode.toxic:
        return 'Toxic';
      case PersonalityMode.witch:
        return 'Witch';
      case PersonalityMode.jealous:
        return 'Jealous';
      case PersonalityMode.dramatic:
        return 'Dramatic';
      case PersonalityMode.sarcastic:
        return 'Sarcastic';
    }
  }

  /// Returns a description of the personality mode
  String get description {
    switch (this) {
      case PersonalityMode.toxic:
        return 'Confrontational and aggressive responses';
      case PersonalityMode.witch:
        return 'Mystical, sarcastic, and vengeful language';
      case PersonalityMode.jealous:
        return 'Possessive reactions and jealous behavior';
      case PersonalityMode.dramatic:
        return 'Over-the-top emotional reactions';
      case PersonalityMode.sarcastic:
        return 'Witty and cutting responses with subtle mockery';
    }
  }

  /// Returns the personality mode from a string value
  static PersonalityMode fromString(String value) {
    switch (value.toLowerCase()) {
      case 'toxic':
        return PersonalityMode.toxic;
      case 'witch':
        return PersonalityMode.witch;
      case 'jealous':
        return PersonalityMode.jealous;
      case 'dramatic':
        return PersonalityMode.dramatic;
      case 'sarcastic':
        return PersonalityMode.sarcastic;
      default:
        return PersonalityMode.toxic; // Default fallback
    }
  }

  /// Returns the string representation of the personality mode
  String get value {
    return toString().split('.').last;
  }
}