import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/widgets/message_bubble.dart';

void main() {
  group('Message Grouping Tests', () {
    late List<Message> testMessages;

    setUp(() {
      final now = DateTime.now();
      testMessages = [
        // First group - user messages
        Message(
          text: 'First user message',
          isUser: true,
          timestamp: now.subtract(const Duration(minutes: 10)),
        ),
        Message(
          text: 'Second user message',
          isUser: true,
          timestamp: now.subtract(const Duration(minutes: 9)),
        ),
        Message(
          text: 'Third user message',
          isUser: true,
          timestamp: now.subtract(const Duration(minutes: 8)),
        ),

        // AI response - single message
        Message(
          text: 'AI response',
          isUser: false,
          timestamp: now.subtract(const Duration(minutes: 7)),
        ),

        // Second group - user messages
        Message(
          text: 'Fourth user message',
          isUser: true,
          timestamp: now.subtract(const Duration(minutes: 5)),
        ),
        Message(
          text: 'Fifth user message',
          isUser: true,
          timestamp: now.subtract(const Duration(minutes: 4)),
        ),
      ];
    });

    testWidgets('should build message list with proper grouping', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ListView.builder(
              itemCount: testMessages.length,
              itemBuilder: (context, index) {
                return MessageBubble(message: testMessages[index]);
              },
            ),
          ),
        ),
      );

      // Verify all messages are displayed
      for (final message in testMessages) {
        expect(find.text(message.text), findsOneWidget);
      }
    });
  });
}
