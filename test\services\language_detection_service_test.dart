import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/services/language_detection_service.dart';

void main() {
  late LanguageDetectionService languageService;

  setUp(() {
    languageService = LanguageDetectionService();
  });

  group('Language Detection Service', () {
    group('Spanish Detection', () {
      test('should detect Spanish with common phrases', () async {
        final result = await languageService.detectLanguage('Hola, ¿cómo estás?');
        expect(result, equals('es'));
      });

      test('should detect Spanish with longer text', () async {
        final result = await languageService.detectLanguage(
          'Buenos días, espero que tengas un buen día. ¿Cómo te va todo?'
        );
        expect(result, equals('es'));
      });

      test('should detect Spanish with special characters', () async {
        final result = await languageService.detectLanguage('Niño pequeño jugando en el jardín');
        expect(result, equals('es'));
      });

      test('should detect Spanish with mixed case', () async {
        final result = await languageService.detectLanguage('GRACIAS por TODO lo que HACES');
        expect(result, equals('es'));
      });
    });

    group('English Detection', () {
      test('should detect English with common phrases', () async {
        final result = await languageService.detectLanguage('Hello, how are you doing today?');
        expect(result, equals('en'));
      });

      test('should detect English with longer text', () async {
        final result = await languageService.detectLanguage(
          'Good morning, I hope you have a wonderful day ahead of you.'
        );
        expect(result, equals('en'));
      });

      test('should detect English with casual language', () async {
        final result = await languageService.detectLanguage('Hey there, what\'s up? How\'s it going?');
        expect(result, equals('en'));
      });
    });

    group('French Detection', () {
      test('should detect French with common phrases', () async {
        final result = await languageService.detectLanguage('Bonjour, comment allez-vous?');
        expect(result, equals('fr'));
      });

      test('should detect French with accented characters', () async {
        final result = await languageService.detectLanguage('C\'est très intéressant, merci beaucoup');
        expect(result, equals('fr'));
      });

      test('should detect French with longer text', () async {
        final result = await languageService.detectLanguage(
          'Je suis très heureux de vous rencontrer aujourd\'hui'
        );
        expect(result, equals('fr'));
      });
    });

    group('Portuguese Detection', () {
      test('should detect Portuguese with common phrases', () async {
        final result = await languageService.detectLanguage('Olá, como você está?');
        expect(result, equals('pt'));
      });

      test('should detect Portuguese with special characters', () async {
        final result = await languageService.detectLanguage('Obrigado pela atenção e compreensão');
        expect(result, equals('pt'));
      });

      test('should detect Portuguese with longer text', () async {
        final result = await languageService.detectLanguage(
          'Bom dia, espero que você tenha um ótimo dia hoje'
        );
        expect(result, equals('pt'));
      });
    });

    group('Italian Detection', () {
      test('should detect Italian with common phrases', () async {
        final result = await languageService.detectLanguage('Ciao, come stai oggi?');
        expect(result, equals('it'));
      });

      test('should detect Italian with accented characters', () async {
        final result = await languageService.detectLanguage('Grazie mille per la tua gentilezza');
        expect(result, equals('it'));
      });

      test('should detect Italian with longer text', () async {
        final result = await languageService.detectLanguage(
          'Buongiorno, spero che tu abbia una bella giornata'
        );
        expect(result, equals('it'));
      });
    });

    group('Edge Cases', () {
      test('should default to English for empty text', () async {
        final result = await languageService.detectLanguage('');
        expect(result, equals('en'));
      });

      test('should default to English for whitespace only', () async {
        final result = await languageService.detectLanguage('   \n\t  ');
        expect(result, equals('en'));
      });

      test('should default to English for numbers only', () async {
        final result = await languageService.detectLanguage('123 456 789');
        expect(result, equals('en'));
      });

      test('should default to English for special characters only', () async {
        final result = await languageService.detectLanguage('!@#\$%^&*()');
        expect(result, equals('en'));
      });

      test('should handle mixed languages by detecting dominant one', () async {
        final result = await languageService.detectLanguage(
          'Hello amigo, como estas? I hope you are bien today'
        );
        // Should detect English as it has more English words
        expect(result, equals('en'));
      });

      test('should handle very short text', () async {
        final result = await languageService.detectLanguage('sí');
        expect(result, equals('es'));
      });
    });

    group('Character Pattern Fallback', () {
      test('should use character patterns when keywords are insufficient', () async {
        // Text with Spanish characters but few common words
        final result = await languageService.detectLanguage('Señorita María José');
        expect(result, equals('es'));
      });

      test('should detect French through accented characters', () async {
        final result = await languageService.detectLanguage('Café français très délicieux');
        expect(result, equals('fr'));
      });

      test('should detect Portuguese through special characters', () async {
        final result = await languageService.detectLanguage('João e Maria são irmãos');
        expect(result, equals('pt'));
      });
    });

    group('Utility Methods', () {
      test('should correctly identify supported languages', () {
        expect(languageService.isLanguageSupported('es'), isTrue);
        expect(languageService.isLanguageSupported('en'), isTrue);
        expect(languageService.isLanguageSupported('fr'), isTrue);
        expect(languageService.isLanguageSupported('pt'), isTrue);
        expect(languageService.isLanguageSupported('it'), isTrue);
        expect(languageService.isLanguageSupported('de'), isFalse);
        expect(languageService.isLanguageSupported('zh'), isFalse);
      });

      test('should return correct language names', () {
        expect(languageService.getLanguageName('es'), equals('Spanish'));
        expect(languageService.getLanguageName('en'), equals('English'));
        expect(languageService.getLanguageName('fr'), equals('French'));
        expect(languageService.getLanguageName('pt'), equals('Portuguese'));
        expect(languageService.getLanguageName('it'), equals('Italian'));
        expect(languageService.getLanguageName('unknown'), equals('Unknown'));
      });

      test('should return all supported languages', () {
        final supportedLanguages = languageService.getSupportedLanguages();
        expect(supportedLanguages, contains('es'));
        expect(supportedLanguages, contains('en'));
        expect(supportedLanguages, contains('fr'));
        expect(supportedLanguages, contains('pt'));
        expect(supportedLanguages, contains('it'));
        expect(supportedLanguages.length, equals(5));
      });
    });

    group('Real-world Examples', () {
      test('should detect Spanish toxic messages', () async {
        final result = await languageService.detectLanguage('Eres un idiota, no me molestes más');
        expect(result, equals('es'));
      });

      test('should detect English toxic messages', () async {
        final result = await languageService.detectLanguage('You are so annoying, leave me alone');
        expect(result, equals('en'));
      });

      test('should detect French emotional messages', () async {
        final result = await languageService.detectLanguage('Tu me manques beaucoup, je suis triste');
        expect(result, equals('fr'));
      });

      test('should detect Portuguese casual conversation', () async {
        final result = await languageService.detectLanguage('Oi, tudo bem? Como foi seu dia?');
        expect(result, equals('pt'));
      });

      test('should detect Italian romantic messages', () async {
        final result = await languageService.detectLanguage('Ti amo molto, sei la mia vita');
        expect(result, equals('it'));
      });
    });

    group('Performance and Accuracy', () {
      test('should handle long text efficiently', () async {
        final longText = 'Hola ' * 1000 + 'como estas muy bien gracias';
        final stopwatch = Stopwatch()..start();
        final result = await languageService.detectLanguage(longText);
        stopwatch.stop();
        
        expect(result, equals('es'));
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should be fast
      });

      test('should maintain accuracy with mixed punctuation', () async {
        final result = await languageService.detectLanguage(
          '¡Hola! ¿Cómo estás? Muy bien, gracias... ¿Y tú?'
        );
        expect(result, equals('es'));
      });

      test('should handle text with emojis and special characters', () async {
        final result = await languageService.detectLanguage(
          'Hello! 😊 How are you doing today? 🌟 Very good thanks! 👍'
        );
        expect(result, equals('en'));
      });
    });
  });
}