import 'package:flutter/material.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/models/personality_mode.dart';

class MessageBubble extends StatelessWidget {
  final Message message;
  final Animation<double>? animation;
  final bool showTimestamp;
  final PersonalityMode? personality;

  const MessageBubble({
    super.key,
    required this.message,
    this.animation,
    this.showTimestamp = true,
    this.personality,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isUser = message.isUser;

    final alignment = isUser
        ? CrossAxisAlignment.end
        : CrossAxisAlignment.start;
    final color = isUser ? colorScheme.primary : colorScheme.surface;
    final textColor = isUser ? colorScheme.onPrimary : colorScheme.onSurface;

    Widget bubble = Column(
      crossAxisAlignment: alignment,
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
          padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 14.0),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Text(
            message.text,
            style: theme.textTheme.bodyLarge?.copyWith(color: textColor),
          ),
        ),
        if (showTimestamp)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              message.getFormattedTimestamp(),
              style: theme.textTheme.bodySmall,
            ),
          ),
      ],
    );

    if (animation != null) {
      return FadeTransition(
        opacity: animation!,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 0.5),
            end: Offset.zero,
          ).animate(animation!),
          child: bubble,
        ),
      );
    }

    return bubble;
  }
}
