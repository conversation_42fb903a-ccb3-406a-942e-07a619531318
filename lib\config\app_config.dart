/// Configuration for the Flutter application
class AppConfig {
  // NOTE: In a production app, you should NEVER store API keys in source code
  // Instead, use environment variables, secure storage, or a backend proxy
  // For this example, we'll use a constant, but consider these alternatives:
  // 1. Use flutter_dotenv package with a .env file
  // 2. Use a backend proxy that handles OpenAI API calls
  // 3. Use secure storage packages like flutter_secure_storage

  static const String _openAiApiKey =
      '********************************************************************************************************************************************************************';

  /// Get OpenAI API key
  /// Replace this with your actual OpenAI API key
  static String get openAiApiKey {
    return _openAiA<PERSON>Key;
  }
}
