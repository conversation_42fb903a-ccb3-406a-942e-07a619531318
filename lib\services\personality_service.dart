import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/personality_mode.dart';
import '../models/personality_configuration.dart';
import '../models/message.dart';
import 'personality_prompts.dart';
import 'emotional_response_service.dart';

/// Service for managing personality configurations and generating personality-based responses
class PersonalityService {
  static const String _configKey = 'personality_configuration';
  PersonalityConfiguration _currentConfig = PersonalityConfiguration.defaultConfig();

  /// Gets the current personality configuration
  PersonalityConfiguration get currentConfig => _currentConfig;

  /// Loads the personality configuration from persistent storage
  Future<void> loadConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString(_configKey);
      
      if (configJson != null) {
        final configMap = jsonDecode(configJson) as Map<String, dynamic>;
        _currentConfig = PersonalityConfiguration.fromJson(configMap);
      }
    } catch (e) {
      // If loading fails, use default configuration
      _currentConfig = PersonalityConfiguration.defaultConfig();
    }
  }

  /// Saves the current personality configuration to persistent storage
  Future<void> saveConfiguration() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = jsonEncode(_currentConfig.toJson());
      await prefs.setString(_configKey, configJson);
    } catch (e) {
      // Handle save error silently - configuration will revert to default on next load
    }
  }

  /// Updates the personality configuration
  Future<void> updateConfiguration(PersonalityConfiguration newConfig) async {
    if (newConfig.isValid()) {
      _currentConfig = newConfig;
      await saveConfiguration();
    }
  }

  /// Changes the current personality mode
  Future<void> changePersonality(PersonalityMode personality) async {
    final newConfig = _currentConfig.copyWith(currentPersonality: personality);
    await updateConfiguration(newConfig);
  }

  /// Sets the emotional intensity level (0.0 to 1.0)
  Future<void> setEmotionalIntensity(double intensity) async {
    if (intensity >= 0.0 && intensity <= 1.0) {
      final newConfig = _currentConfig.copyWith(emotionalIntensity: intensity);
      await updateConfiguration(newConfig);
    }
  }

  /// Toggles emotional responses on/off
  Future<void> toggleEmotionalResponses() async {
    final newConfig = _currentConfig.copyWith(
      enableEmotionalResponses: !_currentConfig.enableEmotionalResponses,
    );
    await updateConfiguration(newConfig);
  }

  /// Sets the preferred language
  Future<void> setPreferredLanguage(String language) async {
    final newConfig = _currentConfig.copyWith(preferredLanguage: language);
    await updateConfiguration(newConfig);
  }

  /// Toggles automatic language detection
  Future<void> toggleAutoDetectLanguage() async {
    final newConfig = _currentConfig.copyWith(
      autoDetectLanguage: !_currentConfig.autoDetectLanguage,
    );
    await updateConfiguration(newConfig);
  }

  /// Generates a personality-based system prompt
  String generateSystemPrompt(String detectedLanguage) {
    if (!_currentConfig.enableEmotionalResponses) {
      // Return a basic prompt if emotional responses are disabled
      return 'You are a helpful AI assistant. Respond naturally and conversationally.';
    }

    final language = _currentConfig.autoDetectLanguage 
        ? detectedLanguage 
        : _currentConfig.preferredLanguage;

    return PersonalityPrompts.getPrompt(_currentConfig.currentPersonality, language);
  }

  /// Generates an emotional response prompt based on user input
  String generateEmotionalPrompt(String userMessage, String detectedLanguage) {
    if (!_currentConfig.enableEmotionalResponses) {
      return generateSystemPrompt(detectedLanguage);
    }

    final language = _currentConfig.autoDetectLanguage 
        ? detectedLanguage 
        : _currentConfig.preferredLanguage;

    return PersonalityPrompts.buildEmotionalResponsePrompt(
      userMessage,
      _currentConfig.currentPersonality,
      language,
      emotionalIntensity: _currentConfig.emotionalIntensity,
    );
  }

  /// Generates an enhanced emotional response prompt with conversation history
  String generateEnhancedEmotionalPrompt(
    String userMessage, 
    String detectedLanguage, 
    List<Message> conversationHistory,
  ) {
    if (!_currentConfig.enableEmotionalResponses) {
      return generateSystemPrompt(detectedLanguage);
    }

    final language = _currentConfig.autoDetectLanguage 
        ? detectedLanguage 
        : _currentConfig.preferredLanguage;

    // Detect emotional intensity from the message
    final emotionalIntensity = EmotionalResponseService.detectEmotionalIntensity(userMessage, language);
    
    // Scale with configured intensity
    final scaledIntensity = (emotionalIntensity * _currentConfig.emotionalIntensity).clamp(0.0, 1.0);

    // Get enhanced emotional response prompt with conversation context
    final conversationContext = _buildConversationContext(conversationHistory, language);
    
    return PersonalityPrompts.buildEmotionalResponsePrompt(
      userMessage,
      _currentConfig.currentPersonality,
      language,
      emotionalIntensity: scaledIntensity,
      conversationContext: conversationContext,
    );
  }

  /// Builds conversation context from message history
  Map<String, dynamic> _buildConversationContext(List<Message> history, String language) {
    if (history.isEmpty) {
      return {'isFirstMessage': true, 'previousTone': 'neutral', 'escalationLevel': 0};
    }
    
    final recentMessages = history.take(5).toList();
    int escalationLevel = 0;
    String previousTone = 'neutral';
    
    for (final message in recentMessages) {
      final intensity = EmotionalResponseService.detectEmotionalIntensity(message.text, language);
      if (intensity > 0.5) {
        escalationLevel++;
      }
      
      if (EmotionalResponseService.containsOffensiveContent(message.text, language)) {
        previousTone = 'offensive';
      } else if (EmotionalResponseService.containsAggressiveLanguage(message.text, language)) {
        previousTone = 'aggressive';
      }
    }
    
    return {
      'isFirstMessage': false,
      'previousTone': previousTone,
      'escalationLevel': escalationLevel,
      'messageCount': history.length,
    };
  }

  /// Detects emotional intensity of a message
  double detectEmotionalIntensity(String message, String language) {
    final detectedLanguage = _currentConfig.autoDetectLanguage 
        ? language 
        : _currentConfig.preferredLanguage;
    
    return EmotionalResponseService.detectEmotionalIntensity(message, detectedLanguage);
  }

  /// Checks if a message contains offensive content
  bool containsOffensiveContent(String message, String language) {
    final detectedLanguage = _currentConfig.autoDetectLanguage 
        ? language 
        : _currentConfig.preferredLanguage;
    
    return EmotionalResponseService.containsOffensiveContent(message, detectedLanguage);
  }

  /// Checks if a message contains aggressive language
  bool containsAggressiveLanguage(String message, String language) {
    final detectedLanguage = _currentConfig.autoDetectLanguage 
        ? language 
        : _currentConfig.preferredLanguage;
    
    return EmotionalResponseService.containsAggressiveLanguage(message, detectedLanguage);
  }

  /// Checks if a message mentions other people (triggers jealousy)
  bool mentionsOtherPeople(String message, String language) {
    final detectedLanguage = _currentConfig.autoDetectLanguage 
        ? language 
        : _currentConfig.preferredLanguage;
    
    return EmotionalResponseService.mentionsOtherPeople(message, detectedLanguage);
  }

  /// Gets all available personality modes
  List<PersonalityMode> getAvailablePersonalities() {
    return PersonalityPrompts.getAllPersonalities();
  }

  /// Checks if a language is supported for the current personality
  bool isLanguageSupported(String language) {
    return PersonalityPrompts.isLanguageSupported(
      _currentConfig.currentPersonality,
      language,
    );
  }

  /// Gets supported languages for the current personality
  List<String> getSupportedLanguages() {
    return PersonalityPrompts.getSupportedLanguages(_currentConfig.currentPersonality);
  }

  /// Resets configuration to default values
  Future<void> resetToDefault() async {
    _currentConfig = PersonalityConfiguration.defaultConfig();
    await saveConfiguration();
  }

  /// Gets a human-readable description of the current configuration
  String getConfigurationDescription() {
    return _currentConfig.getDescription();
  }

  /// Validates if the current configuration is valid
  bool isConfigurationValid() {
    return _currentConfig.isValid();
  }
}