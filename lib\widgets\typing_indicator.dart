import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

class TypingIndicator extends StatefulWidget {
  final bool isVisible;
  final Duration animationDuration;

  const TypingIndicator({
    super.key,
    required this.isVisible,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<TypingIndicator> createState() => _TypingIndicatorState();
}

class _TypingIndicatorState extends State<TypingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _visibilityController;
  late AnimationController _dotsController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // Controller for show/hide animation
    _visibilityController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    // Controller for dots animation
    _dotsController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Fade animation for smooth appearance/disappearance
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _visibilityController,
      curve: Curves.easeInOut,
    ));

    // Slide animation for smooth entry
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _visibilityController,
      curve: Curves.easeOutCubic,
    ));

    // Scale animation for subtle bounce effect
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _visibilityController,
      curve: Curves.elasticOut,
    ));

    // Start animations based on initial visibility
    if (widget.isVisible) {
      _visibilityController.forward();
      _dotsController.repeat();
    }
  }

  @override
  void didUpdateWidget(TypingIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _visibilityController.forward();
        _dotsController.repeat();
      } else {
        _visibilityController.reverse();
        _dotsController.stop();
      }
    }
  }

  @override
  void dispose() {
    _visibilityController.dispose();
    _dotsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Responsive values
    final responsiveBorderRadius = ResponsiveUtils.getResponsiveSpacing(
      context,
      mobile: 22.0,
      tablet: 24.0,
      desktop: 26.0,
    );
    final responsivePadding = ResponsiveUtils.getResponsiveSpacing(
      context,
      mobile: 16.0,
      tablet: 18.0,
      desktop: 20.0,
    );
    final responsiveVerticalPadding = ResponsiveUtils.getResponsiveSpacing(
      context,
      mobile: 12.0,
      tablet: 14.0,
      desktop: 16.0,
    );
    final responsiveDotSpacing = ResponsiveUtils.getResponsiveSpacing(
      context,
      mobile: 4.0,
      tablet: 5.0,
      desktop: 6.0,
    );
    
    return AnimatedBuilder(
      animation: _visibilityController,
      builder: (context, child) {
        if (_visibilityController.isDismissed) {
          return const SizedBox.shrink();
        }

        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                margin: EdgeInsets.only(
                  left: ResponsiveUtils.getResponsiveSpacing(
                    context,
                    mobile: 20.0,
                    tablet: 32.0,
                    desktop: 48.0,
                  ),
                  right: ResponsiveUtils.getResponsiveSpacing(
                    context,
                    mobile: 60.0,
                    tablet: 80.0,
                    desktop: 120.0,
                  ),
                  top: ResponsiveUtils.getResponsiveSpacing(
                    context,
                    mobile: 6.0,
                    tablet: 8.0,
                    desktop: 10.0,
                  ),
                  bottom: ResponsiveUtils.getResponsiveSpacing(
                    context,
                    mobile: 6.0,
                    tablet: 8.0,
                    desktop: 10.0,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: responsivePadding,
                        vertical: responsiveVerticalPadding,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Theme.of(context).colorScheme.surfaceContainerHigh,
                            Theme.of(context).colorScheme.surfaceContainer,
                          ],
                        ),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(responsiveBorderRadius),
                          topRight: Radius.circular(responsiveBorderRadius),
                          bottomLeft: const Radius.circular(6.0),
                          bottomRight: Radius.circular(responsiveBorderRadius),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.15),
                            blurRadius: ResponsiveUtils.getResponsiveSpacing(
                              context,
                              mobile: 12.0,
                              tablet: 14.0,
                              desktop: 16.0,
                            ),
                            offset: const Offset(0, 4),
                            spreadRadius: 2.0,
                          ),
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.08),
                            blurRadius: ResponsiveUtils.getResponsiveSpacing(
                              context,
                              mobile: 20.0,
                              tablet: 22.0,
                              desktop: 24.0,
                            ),
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildDot(0),
                          SizedBox(width: responsiveDotSpacing),
                          _buildDot(1),
                          SizedBox(width: responsiveDotSpacing),
                          _buildDot(2),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                        top: ResponsiveUtils.getResponsiveSpacing(
                          context,
                          mobile: 6.0,
                          tablet: 8.0,
                          desktop: 10.0,
                        ),
                        left: ResponsiveUtils.getResponsiveSpacing(
                          context,
                          mobile: 12.0,
                          tablet: 14.0,
                          desktop: 16.0,
                        ),
                        right: ResponsiveUtils.getResponsiveSpacing(
                          context,
                          mobile: 12.0,
                          tablet: 14.0,
                          desktop: 16.0,
                        ),
                      ),
                      child: Text(
                        'Typing...',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                          fontSize: ResponsiveUtils.getResponsiveFontSize(context, 11.0),
                          fontWeight: FontWeight.w400,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDot(int index) {
    final responsiveDotSize = ResponsiveUtils.getResponsiveSpacing(
      context,
      mobile: 9.0,
      tablet: 10.0,
      desktop: 11.0,
    );
    
    return AnimatedBuilder(
      animation: _dotsController,
      builder: (context, child) {
        // Create staggered animation for each dot
        final delay = index * 0.2;
        final progress = (_dotsController.value + delay) % 1.0;
        
        // Create a pulsing effect
        final opacity = (0.4 + 0.6 * (0.5 + 0.5 * 
            (progress < 0.5 
                ? 4 * progress * progress * progress
                : 1 - 4 * (progress - 1) * (progress - 1) * (progress - 1)))).clamp(0.0, 1.0);
        
        final scale = (0.8 + 0.4 * (0.5 + 0.5 * 
            (progress < 0.5 
                ? 4 * progress * progress * progress
                : 1 - 4 * (progress - 1) * (progress - 1) * (progress - 1)))).clamp(0.6, 1.2);

        final colorScheme = Theme.of(context).colorScheme;
        final dotColor = Color.lerp(
          colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
          colorScheme.secondary.withValues(alpha: 0.9),
          opacity,
        )!;

        return Transform.scale(
          scale: scale,
          child: Container(
            width: responsiveDotSize,
            height: responsiveDotSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: dotColor,
              boxShadow: [
                BoxShadow(
                  color: dotColor.withValues(alpha: opacity * 0.4),
                  blurRadius: ResponsiveUtils.getResponsiveSpacing(
                    context,
                    mobile: 6.0,
                    tablet: 7.0,
                    desktop: 8.0,
                  ),
                  spreadRadius: 1.5,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}