import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/widgets/message_bubble.dart';

void main() {
  group('Message Animation Tests', () {
    late AnimationController animationController;

    setUp(() {
      // Create a test animation controller
      animationController = AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: const TestVSync(),
      );
    });

    tearDown(() {
      animationController.dispose();
    });

    testWidgets('MessageBubble renders without animation', (WidgetTester tester) async {
      final message = Message(
        text: 'Test message',
        isUser: true,
        timestamp: DateTime.now(),
        role: 'user',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(
              message: message,
            ),
          ),
        ),
      );

      expect(find.text('Test message'), findsOneWidget);
      expect(find.byType(MessageBubble), findsOneWidget);
    });

    testWidgets('MessageBubble renders with animation', (WidgetTester tester) async {
      final message = Message(
        text: 'Animated message',
        isUser: false,
        timestamp: DateTime.now(),
        role: 'assistant',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(
              message: message,
              animation: animationController,
            ),
          ),
        ),
      );

      // Initially, the animation should be at 0
      expect(animationController.value, equals(0.0));
      expect(find.text('Animated message'), findsOneWidget);

      // Start the animation
      animationController.forward();
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 150));

      // Animation should be in progress
      expect(animationController.value, greaterThan(0.0));
      expect(animationController.value, lessThan(1.0));

      // Complete the animation
      await tester.pump(const Duration(milliseconds: 300));
      expect(animationController.value, equals(1.0));
    });

    testWidgets('User and AI messages have different slide directions', (WidgetTester tester) async {
      final userMessage = Message(
        text: 'User message',
        isUser: true,
        timestamp: DateTime.now(),
        role: 'user',
      );

      final aiMessage = Message(
        text: 'AI message',
        isUser: false,
        timestamp: DateTime.now(),
        role: 'assistant',
      );

      // Test user message animation
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                MessageBubble(
                  message: userMessage,
                  animation: animationController,
                ),
                MessageBubble(
                  message: aiMessage,
                  animation: animationController,
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('User message'), findsOneWidget);
      expect(find.text('AI message'), findsOneWidget);
      expect(find.byType(SlideTransition), findsAtLeastNWidgets(2));
      expect(find.byType(FadeTransition), findsAtLeastNWidgets(2));
    });

    testWidgets('Animation completes successfully', (WidgetTester tester) async {
      final message = Message(
        text: 'Test animation completion',
        isUser: true,
        timestamp: DateTime.now(),
        role: 'user',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(
              message: message,
              animation: animationController,
            ),
          ),
        ),
      );

      // Start animation
      animationController.forward();
      
      // Pump through the entire animation duration
      await tester.pumpAndSettle();
      
      // Animation should be complete
      expect(animationController.status, equals(AnimationStatus.completed));
      expect(animationController.value, equals(1.0));
      
      // Message should still be visible
      expect(find.text('Test animation completion'), findsOneWidget);
    });

    testWidgets('Animation handles disposal correctly', (WidgetTester tester) async {
      final message = Message(
        text: 'Disposal test',
        isUser: false,
        timestamp: DateTime.now(),
        role: 'assistant',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(
              message: message,
              animation: animationController,
            ),
          ),
        ),
      );

      // Start animation
      animationController.forward();
      await tester.pump(const Duration(milliseconds: 50));

      // Stop animation before removing widget
      animationController.stop();
      await tester.pump();

      // Remove the widget
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SizedBox.shrink(),
          ),
        ),
      );

      // Animation controller should still be valid (managed by parent)
      expect(animationController.status, isIn([
        AnimationStatus.forward,
        AnimationStatus.completed,
        AnimationStatus.dismissed,
        AnimationStatus.reverse,
      ]));
    });

    group('Animation Timing Tests', () {
      testWidgets('User message animation is faster than AI message', (WidgetTester tester) async {
        // This test verifies that the timing differences are handled correctly
        // The actual timing is controlled by the ChatScreen, but we can test
        // that the MessageBubble handles different animation controllers properly
        
        final userController = AnimationController(
          duration: const Duration(milliseconds: 250),
          vsync: const TestVSync(),
        );
        
        final aiController = AnimationController(
          duration: const Duration(milliseconds: 400),
          vsync: const TestVSync(),
        );

        final userMessage = Message(
          text: 'Fast user message',
          isUser: true,
          timestamp: DateTime.now(),
          role: 'user',
        );

        final aiMessage = Message(
          text: 'Slower AI message',
          isUser: false,
          timestamp: DateTime.now(),
          role: 'assistant',
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  MessageBubble(
                    message: userMessage,
                    animation: userController,
                  ),
                  MessageBubble(
                    message: aiMessage,
                    animation: aiController,
                  ),
                ],
              ),
            ),
          ),
        );

        // Start both animations
        userController.forward();
        aiController.forward();

        // Pump and settle to complete animations
        await tester.pumpAndSettle();
        
        // Both animations should eventually complete, but user should be faster
        expect(userController.duration!.inMilliseconds, lessThan(aiController.duration!.inMilliseconds));
        expect(userController.status, equals(AnimationStatus.completed));
        expect(aiController.status, equals(AnimationStatus.completed));

        // Clean up
        userController.dispose();
        aiController.dispose();
      });
    });
  });
}