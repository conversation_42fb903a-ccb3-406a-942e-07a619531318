import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:toxic_chat/models/personality_mode.dart';
import 'package:toxic_chat/providers/theme_provider.dart';
import 'package:toxic_chat/services/personality_service.dart';
import 'package:toxic_chat/widgets/personality_selector.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final PersonalityService _personalityService = PersonalityService();

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: FutureBuilder(
        future: _personalityService.loadConfiguration(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          return ListView(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            children: [
              _buildSectionTitle(context, "Appearance"),
              _buildSettingsCard(context, [
                SwitchListTile(
                  title: const Text('Dark Mode'),
                  value: themeProvider.darkTheme,
                  onChanged: (value) {
                    themeProvider.toggleTheme();
                  },
                  secondary: const Icon(Icons.palette_outlined),
                ),
              ]),
              const SizedBox(height: 20),
              _buildSectionTitle(context, "AI Behavior"),
              _buildSettingsCard(context, [
                ListTile(
                  leading: const Icon(Icons.person_outline),
                  title: const Text('Personality'),
                  subtitle: Text(
                    _personalityService
                        .currentConfig
                        .currentPersonality
                        .displayName,
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () async {
                    await showPersonalitySelector(
                      context: context,
                      currentPersonality:
                          _personalityService.currentConfig.currentPersonality,
                      onPersonalityChanged: (personality) {
                        setState(() {
                          _personalityService.changePersonality(personality);
                        });
                      },
                    );
                  },
                ),
                const Divider(height: 0, indent: 56),
                SwitchListTile(
                  title: const Text('Enable Emotional Responses'),
                  value: _personalityService
                      .currentConfig
                      .enableEmotionalResponses,
                  onChanged: (value) {
                    setState(() {
                      _personalityService.toggleEmotionalResponses();
                    });
                  },
                  secondary: const Icon(Icons.auto_awesome_outlined),
                ),
                const Divider(height: 0, indent: 56),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Emotional Intensity',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(
                            Icons.sentiment_very_dissatisfied,
                            color: Colors.grey,
                          ),
                          Expanded(
                            child: Slider(
                              value: _personalityService
                                  .currentConfig
                                  .emotionalIntensity,
                              onChanged: (value) {
                                setState(() {
                                  _personalityService.setEmotionalIntensity(
                                    value,
                                  );
                                });
                              },
                            ),
                          ),
                          const Icon(
                            Icons.sentiment_very_satisfied,
                            color: Colors.grey,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ]),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, bottom: 8.0, top: 16.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildSettingsCard(BuildContext context, List<Widget> children) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: Column(children: children),
    );
  }
}
