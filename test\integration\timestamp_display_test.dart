import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/widgets/message_bubble.dart';

void main() {
  group('Timestamp Display Integration Tests', () {
    testWidgets('should display timestamps in chat bubbles', (
      WidgetTester tester,
    ) async {
      final now = DateTime.now();

      final recentMessage = Message(
        text: 'Recent message',
        isUser: true,
        timestamp: now.subtract(const Duration(seconds: 30)),
      );

      final oldMessage = Message(
        text: 'Old message',
        isUser: false,
        timestamp: now.subtract(const Duration(minutes: 15)),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                MessageBubble(message: recentMessage, showTimestamp: true),
                MessageBubble(message: oldMessage, showTimestamp: true),
              ],
            ),
          ),
        ),
      );

      // Verify messages are displayed
      expect(find.text('Recent message'), findsOneWidget);
      expect(find.text('Old message'), findsOneWidget);

      // Verify timestamps are displayed
      expect(find.text('Just now'), findsOneWidget);
      expect(find.text('15m ago'), findsOneWidget);
    });

    testWidgets('should hide timestamps when showTimestamp is false', (
      WidgetTester tester,
    ) async {
      final message = Message(
        text: 'Test message',
        isUser: true,
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message, showTimestamp: false),
          ),
        ),
      );

      // Verify message is displayed but timestamp is not
      expect(find.text('Test message'), findsOneWidget);
      expect(find.text('5m ago'), findsNothing);
    });

    testWidgets('should apply responsive timestamp styling', (
      WidgetTester tester,
    ) async {
      final message = Message(
        text: 'Responsive test',
        isUser: true,
        timestamp: DateTime.now().subtract(const Duration(minutes: 10)),
      );

      // Test on different screen sizes
      await tester.binding.setSurfaceSize(const Size(400, 800)); // Mobile
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message, showTimestamp: true),
          ),
        ),
      );

      // Find timestamp text widget
      final timestampFinder = find.text('10m ago');
      expect(timestampFinder, findsOneWidget);

      final timestampWidget = tester.widget<Text>(timestampFinder);
      expect(timestampWidget.style?.fontSize, greaterThan(10.0));
      expect(timestampWidget.style?.fontSize, lessThan(15.0));
    });

    testWidgets(
      'should position timestamps correctly for user vs AI messages',
      (WidgetTester tester) async {
        final userMessage = Message(
          text: 'User message',
          isUser: true,
          timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        );

        final aiMessage = Message(
          text: 'AI message',
          isUser: false,
          timestamp: DateTime.now().subtract(const Duration(minutes: 3)),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  MessageBubble(message: userMessage, showTimestamp: true),
                  MessageBubble(message: aiMessage, showTimestamp: true),
                ],
              ),
            ),
          ),
        );

        // Verify both messages and timestamps are displayed
        expect(find.text('User message'), findsOneWidget);
        expect(find.text('AI message'), findsOneWidget);
        expect(find.text('5m ago'), findsOneWidget);
        expect(find.text('3m ago'), findsOneWidget);

        // Verify timestamps have proper styling
        final userTimestamp = tester.widget<Text>(find.text('5m ago'));
        final aiTimestamp = tester.widget<Text>(find.text('3m ago'));

        expect(userTimestamp.style?.color, isNotNull);
        expect(aiTimestamp.style?.color, isNotNull);
      },
    );
  });
}
