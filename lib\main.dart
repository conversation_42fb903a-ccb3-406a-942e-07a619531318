import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:toxic_chat/config/app_theme.dart';
import 'package:toxic_chat/providers/theme_provider.dart';
import 'screens/chat_screen.dart';

void main() {
  runApp(
    ChangeNotifierProvider(
      create: (_) => ThemeProvider(),
      child: const ToxicChatApp(),
    ),
  );
}

class ToxicChatApp extends StatelessWidget {
  const ToxicChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return MaterialApp(
      title: 'Toxic Chat App',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeProvider.darkTheme ? ThemeMode.dark : ThemeMode.light,
      home: const ChatScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
