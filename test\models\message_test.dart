import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/models/personality_mode.dart';

void main() {
  group('Message', () {
    late DateTime testTimestamp;
    late Message userMessage;
    late Message assistantMessage;
    late Message systemMessage;

    setUp(() {
      testTimestamp = DateTime(2024, 1, 15, 10, 30, 0);
      userMessage = Message(
        text: 'Hello there!',
        isUser: true,
        timestamp: testTimestamp,
        role: 'user',
      );
      assistantMessage = Message(
        text: 'Hi! How are you?',
        isUser: false,
        timestamp: testTimestamp,
        role: 'assistant',
      );
      systemMessage = Message(
        text: 'System message',
        isUser: false,
        timestamp: testTimestamp,
        role: 'system',
      );
    });

    test('should create Message with all required fields', () {
      expect(userMessage.text, equals('Hello there!'));
      expect(userMessage.isUser, isTrue);
      expect(userMessage.timestamp, equals(testTimestamp));
      expect(userMessage.role, equals('user'));
    });

    test('should create Message without role (nullable)', () {
      final messageWithoutRole = Message(
        text: 'Test message',
        isUser: true,
        timestamp: testTimestamp,
      );

      expect(messageWithoutRole.role, isNull);
    });

    group('JSON serialization', () {
      test('should convert Message to JSON correctly', () {
        final json = userMessage.toJson();

        expect(json['text'], equals('Hello there!'));
        expect(json['isUser'], isTrue);
        expect(json['timestamp'], equals(testTimestamp.toIso8601String()));
        expect(json['role'], equals('user'));
      });

      test('should convert Message with null role to JSON correctly', () {
        final messageWithoutRole = Message(
          text: 'Test message',
          isUser: true,
          timestamp: testTimestamp,
        );
        final json = messageWithoutRole.toJson();

        expect(json['role'], isNull);
      });

      test('should create Message from JSON correctly', () {
        final json = {
          'text': 'Hello from JSON!',
          'isUser': false,
          'timestamp': testTimestamp.toIso8601String(),
          'role': 'assistant',
        };

        final message = Message.fromJson(json);

        expect(message.text, equals('Hello from JSON!'));
        expect(message.isUser, isFalse);
        expect(message.timestamp, equals(testTimestamp));
        expect(message.role, equals('assistant'));
      });

      test('should handle JSON with null role', () {
        final json = {
          'text': 'Test message',
          'isUser': true,
          'timestamp': testTimestamp.toIso8601String(),
          'role': null,
        };

        final message = Message.fromJson(json);

        expect(message.role, isNull);
      });

      test('should round-trip JSON serialization correctly', () {
        final originalMessage = userMessage;
        final json = originalMessage.toJson();
        final deserializedMessage = Message.fromJson(json);

        expect(deserializedMessage, equals(originalMessage));
      });
    });

    group('OpenAI API format conversion', () {
      test('should convert user message to OpenAI format correctly', () {
        final openAIFormat = userMessage.toOpenAIFormat();

        expect(openAIFormat['role'], equals('user'));
        expect(openAIFormat['content'], equals('Hello there!'));
      });

      test('should convert assistant message to OpenAI format correctly', () {
        final openAIFormat = assistantMessage.toOpenAIFormat();

        expect(openAIFormat['role'], equals('assistant'));
        expect(openAIFormat['content'], equals('Hi! How are you?'));
      });

      test('should convert system message to OpenAI format correctly', () {
        final openAIFormat = systemMessage.toOpenAIFormat();

        expect(openAIFormat['role'], equals('system'));
        expect(openAIFormat['content'], equals('System message'));
      });

      test(
        'should default to user role when role is null and isUser is true',
        () {
          final messageWithoutRole = Message(
            text: 'Test message',
            isUser: true,
            timestamp: testTimestamp,
          );
          final openAIFormat = messageWithoutRole.toOpenAIFormat();

          expect(openAIFormat['role'], equals('user'));
          expect(openAIFormat['content'], equals('Test message'));
        },
      );

      test(
        'should default to assistant role when role is null and isUser is false',
        () {
          final messageWithoutRole = Message(
            text: 'Assistant message',
            isUser: false,
            timestamp: testTimestamp,
          );
          final openAIFormat = messageWithoutRole.toOpenAIFormat();

          expect(openAIFormat['role'], equals('assistant'));
          expect(openAIFormat['content'], equals('Assistant message'));
        },
      );
    });

    group('equality and hashCode', () {
      test('should be equal when all fields match', () {
        final message1 = Message(
          text: 'Same message',
          isUser: true,
          timestamp: testTimestamp,
          role: 'user',
        );
        final message2 = Message(
          text: 'Same message',
          isUser: true,
          timestamp: testTimestamp,
          role: 'user',
        );

        expect(message1, equals(message2));
        expect(message1.hashCode, equals(message2.hashCode));
      });

      test('should not be equal when text differs', () {
        final message1 = Message(
          text: 'Different message',
          isUser: true,
          timestamp: testTimestamp,
          role: 'user',
        );
        final message2 = Message(
          text: 'Another message',
          isUser: true,
          timestamp: testTimestamp,
          role: 'user',
        );

        expect(message1, isNot(equals(message2)));
      });

      test('should not be equal when isUser differs', () {
        final message1 = Message(
          text: 'Same message',
          isUser: true,
          timestamp: testTimestamp,
          role: 'user',
        );
        final message2 = Message(
          text: 'Same message',
          isUser: false,
          timestamp: testTimestamp,
          role: 'user',
        );

        expect(message1, isNot(equals(message2)));
      });
    });

    test('should have meaningful toString representation', () {
      final stringRepresentation = userMessage.toString();

      expect(stringRepresentation, contains('Hello there!'));
      expect(stringRepresentation, contains('true'));
      expect(stringRepresentation, contains('user'));
    });
  });

  group('Enhanced Metadata', () {
    test('should create Message with language and personality fields', () {
      final enhancedMessage = Message(
        text: 'Hola, ¿cómo estás?',
        isUser: false,
        timestamp: DateTime.now(),
        role: 'assistant',
        language: 'spanish',
        personality: PersonalityMode.toxic,
      );

      expect(enhancedMessage.language, equals('spanish'));
      expect(enhancedMessage.personality, equals(PersonalityMode.toxic));
    });

    test('should handle null language and personality fields', () {
      final basicMessage = Message(
        text: 'Basic message',
        isUser: true,
        timestamp: DateTime.now(),
      );

      expect(basicMessage.language, isNull);
      expect(basicMessage.personality, isNull);
    });

    group('JSON serialization with enhanced metadata', () {
      test('should serialize language and personality to JSON', () {
        final enhancedMessage = Message(
          text: 'Test message',
          isUser: false,
          timestamp: DateTime(2024, 1, 15, 10, 30, 0),
          role: 'assistant',
          language: 'spanish',
          personality: PersonalityMode.witch,
        );

        final json = enhancedMessage.toJson();

        expect(json['language'], equals('spanish'));
        expect(json['personality'], equals('witch'));
      });

      test('should deserialize language and personality from JSON', () {
        final json = {
          'text': 'Test message',
          'isUser': false,
          'timestamp': DateTime(2024, 1, 15, 10, 30, 0).toIso8601String(),
          'role': 'assistant',
          'language': 'english',
          'personality': 'sarcastic',
        };

        final message = Message.fromJson(json);

        expect(message.language, equals('english'));
        expect(message.personality, equals(PersonalityMode.sarcastic));
      });

      test('should handle null language and personality in JSON', () {
        final json = {
          'text': 'Test message',
          'isUser': true,
          'timestamp': DateTime(2024, 1, 15, 10, 30, 0).toIso8601String(),
          'role': 'user',
          'language': null,
          'personality': null,
        };

        final message = Message.fromJson(json);

        expect(message.language, isNull);
        expect(message.personality, isNull);
      });

      test('should round-trip JSON serialization with enhanced metadata', () {
        final originalMessage = Message(
          text: 'Enhanced message',
          isUser: false,
          timestamp: DateTime(2024, 1, 15, 10, 30, 0),
          role: 'assistant',
          language: 'french',
          personality: PersonalityMode.dramatic,
        );

        final json = originalMessage.toJson();
        final deserializedMessage = Message.fromJson(json);

        expect(deserializedMessage, equals(originalMessage));
        expect(deserializedMessage.language, equals('french'));
        expect(
          deserializedMessage.personality,
          equals(PersonalityMode.dramatic),
        );
      });
    });

    group('equality with enhanced metadata', () {
      test('should be equal when all fields including metadata match', () {
        final message1 = Message(
          text: 'Same message',
          isUser: false,
          timestamp: DateTime(2024, 1, 15, 10, 30, 0),
          role: 'assistant',
          language: 'spanish',
          personality: PersonalityMode.jealous,
        );
        final message2 = Message(
          text: 'Same message',
          isUser: false,
          timestamp: DateTime(2024, 1, 15, 10, 30, 0),
          role: 'assistant',
          language: 'spanish',
          personality: PersonalityMode.jealous,
        );

        expect(message1, equals(message2));
        expect(message1.hashCode, equals(message2.hashCode));
      });

      test('should not be equal when language differs', () {
        final message1 = Message(
          text: 'Same message',
          isUser: false,
          timestamp: DateTime(2024, 1, 15, 10, 30, 0),
          role: 'assistant',
          language: 'spanish',
          personality: PersonalityMode.toxic,
        );
        final message2 = Message(
          text: 'Same message',
          isUser: false,
          timestamp: DateTime(2024, 1, 15, 10, 30, 0),
          role: 'assistant',
          language: 'english',
          personality: PersonalityMode.toxic,
        );

        expect(message1, isNot(equals(message2)));
      });

      test('should not be equal when personality differs', () {
        final message1 = Message(
          text: 'Same message',
          isUser: false,
          timestamp: DateTime(2024, 1, 15, 10, 30, 0),
          role: 'assistant',
          language: 'spanish',
          personality: PersonalityMode.toxic,
        );
        final message2 = Message(
          text: 'Same message',
          isUser: false,
          timestamp: DateTime(2024, 1, 15, 10, 30, 0),
          role: 'assistant',
          language: 'spanish',
          personality: PersonalityMode.witch,
        );

        expect(message1, isNot(equals(message2)));
      });
    });
  });

  group('Message Grouping Detection', () {
    test(
      'should group consecutive messages from same sender within time limit',
      () {
        final timestamp1 = DateTime(2024, 1, 15, 10, 30, 0);
        final timestamp2 = timestamp1.add(const Duration(minutes: 1));

        final message1 = Message(
          text: 'First message',
          isUser: true,
          timestamp: timestamp1,
        );
        final message2 = Message(
          text: 'Second message',
          isUser: true,
          timestamp: timestamp2,
        );

        expect(message2.shouldGroupWithPrevious(message1), isTrue);
        expect(message1.shouldGroupWithNext(message2), isTrue);
      },
    );

    test('should not group messages from different senders', () {
      final timestamp = DateTime(2024, 1, 15, 10, 30, 0);

      final userMessage = Message(
        text: 'User message',
        isUser: true,
        timestamp: timestamp,
      );
      final aiMessage = Message(
        text: 'AI message',
        isUser: false,
        timestamp: timestamp.add(const Duration(seconds: 30)),
      );

      expect(aiMessage.shouldGroupWithPrevious(userMessage), isFalse);
      expect(userMessage.shouldGroupWithNext(aiMessage), isFalse);
    });

    test('should not group messages with large time gaps', () {
      final timestamp1 = DateTime(2024, 1, 15, 10, 30, 0);
      final timestamp2 = timestamp1.add(const Duration(minutes: 5));

      final message1 = Message(
        text: 'First message',
        isUser: true,
        timestamp: timestamp1,
      );
      final message2 = Message(
        text: 'Second message',
        isUser: true,
        timestamp: timestamp2,
      );

      expect(message2.shouldGroupWithPrevious(message1), isFalse);
      expect(message1.shouldGroupWithNext(message2), isFalse);
    });
  });

  group('Timestamp Formatting', () {
    test('should format recent timestamps correctly', () {
      final now = DateTime.now();

      // Just now
      final justNowMessage = Message(
        text: 'Just now message',
        isUser: true,
        timestamp: now.subtract(const Duration(seconds: 30)),
      );
      expect(justNowMessage.getFormattedTimestamp(), equals('Just now'));

      // Minutes ago
      final minutesAgoMessage = Message(
        text: 'Minutes ago message',
        isUser: true,
        timestamp: now.subtract(const Duration(minutes: 5)),
      );
      expect(minutesAgoMessage.getFormattedTimestamp(), equals('5m ago'));

      // Hours ago
      final hoursAgoMessage = Message(
        text: 'Hours ago message',
        isUser: true,
        timestamp: now.subtract(const Duration(hours: 2)),
      );
      expect(hoursAgoMessage.getFormattedTimestamp(), equals('2h ago'));
    });

    test('should format old timestamps with date', () {
      final oldTimestamp = DateTime(2024, 1, 15, 10, 30, 0);
      final oldMessage = Message(
        text: 'Old message',
        isUser: true,
        timestamp: oldTimestamp,
      );

      expect(oldMessage.getFormattedTimestamp(), equals('01/15'));
    });
  });
}
