import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/models/personality_mode.dart';
import 'package:toxic_chat/services/language_detection_service.dart';
import 'package:toxic_chat/services/chat_service.dart';

void main() {
  group('Chat Language Flow Tests', () {
    late LanguageDetectionService languageService;
    late ChatService chatService;

    setUp(() {
      languageService = LanguageDetectionService();
      chatService = ChatService();
    });

    test('Language detection works with Spanish input', () async {
      final spanishText = 'Hola, ¿cómo estás?';
      final detectedLanguage = await languageService.detectLanguage(spanishText);
      
      expect(detectedLanguage, equals('es'));
      expect(languageService.getLanguageName(detectedLanguage), equals('Spanish'));
    });

    test('Language detection works with English input', () async {
      final englishText = 'Hello, how are you?';
      final detectedLanguage = await languageService.detectLanguage(englishText);
      
      expect(detectedLanguage, equals('en'));
      expect(languageService.getLanguageName(detectedLanguage), equals('English'));
    });

    test('Message model stores language information correctly', () {
      final message = Message(
        text: 'Hola mundo',
        isUser: true,
        timestamp: DateTime.now(),
        role: 'user',
        language: 'es',
      );

      expect(message.language, equals('es'));
      expect(message.text, equals('Hola mundo'));
      expect(message.isUser, isTrue);
    });

    test('Message model stores personality information correctly', () {
      final message = Message(
        text: 'AI response',
        isUser: false,
        timestamp: DateTime.now(),
        role: 'assistant',
        language: 'en',
        personality: PersonalityMode.toxic,
      );

      expect(message.personality, equals(PersonalityMode.toxic));
      expect(message.language, equals('en'));
      expect(message.isUser, isFalse);
    });

    test('ChatService accepts language and personality parameters', () async {
      final userMessage = Message(
        text: 'Test message',
        isUser: true,
        timestamp: DateTime.now(),
        role: 'user',
        language: 'en',
      );

      // This test verifies the method signature accepts the parameters
      // We'll test that the method can be called without compilation errors
      try {
        final response = await chatService.sendMessage(
          [userMessage],
          personality: PersonalityMode.toxic,
          language: 'en',
        );
        
        // If the call succeeds, verify the response has the expected properties
        expect(response.isUser, isFalse);
        expect(response.personality, equals(PersonalityMode.toxic));
        expect(response.language, equals('en'));
      } catch (e) {
        // If it fails due to API issues, that's also acceptable for this test
        // We just want to verify the method signature works
        expect(e, isA<Exception>());
      }
    });

    test('Language consistency is maintained throughout conversation', () async {
      // Simulate a conversation flow
      final messages = <Message>[];
      
      // First message in Spanish
      final spanishMessage = Message(
        text: 'Hola',
        isUser: true,
        timestamp: DateTime.now(),
        role: 'user',
        language: 'es',
      );
      messages.add(spanishMessage);
      
      // AI response should also be in Spanish
      final aiResponse = Message(
        text: 'Hola, ¿qué tal?',
        isUser: false,
        timestamp: DateTime.now(),
        role: 'assistant',
        language: 'es',
        personality: PersonalityMode.toxic,
      );
      messages.add(aiResponse);
      
      // Verify language consistency
      expect(messages[0].language, equals('es'));
      expect(messages[1].language, equals('es'));
      expect(messages[1].personality, equals(PersonalityMode.toxic));
    });

    test('Language switching is detected correctly', () async {
      // Test switching from English to Spanish
      final englishText = 'Hello there';
      final spanishText = 'Hola amigo';
      
      final englishLang = await languageService.detectLanguage(englishText);
      final spanishLang = await languageService.detectLanguage(spanishText);
      
      expect(englishLang, equals('en'));
      expect(spanishLang, equals('es'));
      expect(englishLang, isNot(equals(spanishLang)));
    });
  });
}