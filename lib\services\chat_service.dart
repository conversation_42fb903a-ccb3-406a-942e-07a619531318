import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/message.dart';
import '../models/personality_mode.dart';
import 'openai_service.dart';

/// Exception thrown when chat service encounters an error
class ChatServiceException implements Exception {
  final String message;
  final int? statusCode;
  final ChatServiceErrorType errorType;
  final bool isRetryable;
  
  const ChatServiceException(
    this.message, [
    this.statusCode,
    this.errorType = ChatServiceErrorType.unknown,
    this.isRetryable = true,
  ]);
  
  @override
  String toString() => 'ChatServiceException: $message';
}

/// Types of chat service errors for better error handling
enum ChatServiceErrorType {
  network,
  timeout,
  authentication,
  rateLimit,
  serverError,
  validation,
  unknown,
}

/// Service for communicating directly with OpenAI API
class ChatService {
  final OpenAIService _openAiService;
  
  ChatService({
    OpenAIService? openAiService,
  }) : _openAiService = openAiService ?? OpenAIService();

  /// Send a chat message and get AI response
  /// 
  /// Takes a list of [messages] representing the conversation history
  /// Optional [personality] parameter to specify AI personality mode
  /// Optional [language] parameter to specify response language
  /// Returns the AI assistant's response as a [Message]
  /// 
  /// Throws [ChatServiceException] on error
  Future<Message> sendMessage(
    List<Message> messages, {
    PersonalityMode personality = PersonalityMode.toxic,
    String language = 'english',
  }) async {
    print('💬 ChatService: Starting sendMessage...');
    print('💬 ChatService: Messages count: ${messages.length}');
    
    if (messages.isEmpty) {
      print('❌ ChatService: Messages list is empty');
      throw const ChatServiceException(
        'Messages list cannot be empty',
        null,
        ChatServiceErrorType.validation,
        false,
      );
    }

    // Validate that the last message is from the user
    final lastMessage = messages.last;
    print('💬 ChatService: Last message is from user: ${lastMessage.isUser}');
    
    if (!lastMessage.isUser) {
      print('❌ ChatService: Last message is not from user');
      throw const ChatServiceException(
        'Last message must be from user',
        null,
        ChatServiceErrorType.validation,
        false,
      );
    }

    try {
      // Convert messages to OpenAI format
      final openAiMessages = _convertMessagesToOpenAiFormat(messages);
      print('💬 ChatService: Converted messages: $openAiMessages');
      
      // Call OpenAI service directly with personality and language
      print('💬 ChatService: Calling OpenAI service with personality: ${personality.displayName}, language: $language');
      final aiResponse = await _openAiService.sendChatRequest(
        openAiMessages,
        personality: personality,
        language: language,
      );
      print('💬 ChatService: Received AI response: $aiResponse');
      
      // Create Message object for AI response with personality and language info
      final message = Message(
        text: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
        role: 'assistant',
        language: language,
        personality: personality,
      );
      print('✅ ChatService: Successfully created response message with personality: ${personality.displayName}, language: $language');
      
      return message;
    } on SocketException catch (e) {
      print('❌ ChatService: SocketException: $e');
      throw const ChatServiceException(
        'Unable to connect to OpenAI service. Please check your connection.',
        null,
        ChatServiceErrorType.network,
        true,
      );
    } on OpenAIException catch (e) {
      print('❌ ChatService: OpenAIException: $e');
      
      // Handle OpenAI specific errors
      ChatServiceErrorType errorType = ChatServiceErrorType.serverError;
      bool isRetryable = true;
      
      if (e.message.contains('Invalid API key')) {
        errorType = ChatServiceErrorType.authentication;
        isRetryable = false;
        print('❌ ChatService: Authentication error detected');
      } else if (e.message.contains('Rate limit exceeded')) {
        errorType = ChatServiceErrorType.rateLimit;
        isRetryable = true;
        print('❌ ChatService: Rate limit error detected');
      } else if (e.message.contains('timed out')) {
        errorType = ChatServiceErrorType.timeout;
        isRetryable = true;
        print('❌ ChatService: Timeout error detected');
      }
      
      throw ChatServiceException(
        'AI service error: ${e.message}',
        null,
        errorType,
        isRetryable,
      );
    } on http.ClientException catch (e) {
      print('❌ ChatService: ClientException: $e');
      throw ChatServiceException(
        'Network error: ${e.message}',
        null,
        ChatServiceErrorType.network,
        true,
      );
    } catch (e) {
      print('❌ ChatService: Unexpected error: $e');
      print('❌ ChatService: Error type: ${e.runtimeType}');
      
      if (e is ChatServiceException) rethrow;
      throw ChatServiceException(
        'Unexpected error: $e',
        null,
        ChatServiceErrorType.unknown,
        true,
      );
    }
  }

  /// Check if the OpenAI service is available
  /// 
  /// Returns true if the API key is configured
  Future<bool> isServiceAvailable() async {
    try {
      return true; // Always available if we reach here (API key validation happens on first request)
    } catch (e) {
      return false;
    }
  }

  /// Convert Flutter Message objects to OpenAI API format
  List<Map<String, dynamic>> _convertMessagesToOpenAiFormat(List<Message> messages) {
    return messages.map((message) => {
      'role': message.isUser ? 'user' : 'assistant',
      'content': message.text,
    }).toList();
  }

  /// Dispose of resources
  void dispose() {
    _openAiService.dispose();
  }
}