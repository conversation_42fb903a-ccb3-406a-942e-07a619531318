import 'package:flutter/material.dart';
import '../models/message.dart';
import '../widgets/message_bubble.dart';

/// Manager class for chat layout and styling
class ChatLayoutManager {
  /// Build a message bubble with proper styling and layout
  static Widget buildMessageBubble({
    required Message message,
    required List<Message> allMessages,
    required int messageIndex,
    bool showTimestamp = true,
    Animation<double>? animation,
  }) {
    return MessageBubble(
      message: message,
      showTimestamp: showTimestamp,
      animation: animation,
    );
  }
}
