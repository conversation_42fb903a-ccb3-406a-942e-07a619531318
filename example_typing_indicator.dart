import 'package:flutter/material.dart';
import 'lib/widgets/typing_indicator.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Typing Indicator Demo',
      theme: ThemeData.dark(),
      home: const TypingIndicatorDemo(),
    );
  }
}

class TypingIndicatorDemo extends StatefulWidget {
  const TypingIndicatorDemo({super.key});

  @override
  State<TypingIndicatorDemo> createState() => _TypingIndicatorDemoState();
}

class _TypingIndicatorDemoState extends State<TypingIndicatorDemo> {
  bool _isVisible = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Typing Indicator Demo')),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                const SizedBox(height: 20),
                TypingIndicator(isVisible: _isVisible),
                const SizedBox(height: 20),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  _isVisible = !_isVisible;
                });
              },
              child: Text(_isVisible ? 'Hide Typing' : 'Show Typing'),
            ),
          ),
        ],
      ),
    );
  }
}