import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:test/test.dart';
import '../../lib/services/openai_service.dart';

// Mock HTTP client for testing
class MockHttpClient extends http.BaseClient {
  final http.Response Function(http.BaseRequest request) _handler;
  
  MockHttpClient(this._handler);
  
  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    final response = _handler(request);
    return http.StreamedResponse(
      Stream.fromIterable([response.bodyBytes]),
      response.statusCode,
      headers: response.headers,
    );
  }
}

void main() {
  group('OpenAIService', () {
    late OpenAIService service;
    late MockHttpClient mockClient;
    const testApiKey = 'test-api-key-123';

    tearDown(() {
      service.dispose();
    });

    group('sendChatRequest', () {
      test('should return AI response for valid request', () async {
        // Arrange
        final mockResponse = {
          'choices': [
            {
              'message': {
                'role': 'assistant',
                'content': 'Oh so NOW you want to talk to me???'
              }
            }
          ]
        };

        mockClient = MockHttpClient((request) {
          // Verify request structure
          expect(request.url.toString(), contains('chat/completions'));
          expect(request.headers['Content-Type'], startsWith('application/json'));
          expect(request.headers['Authorization'], startsWith('Bearer '));
          
          // Verify request body contains system prompt and messages
          final body = json.decode((request as http.Request).body);
          expect(body['model'], equals('gpt-3.5-turbo'));
          expect(body['messages'], isA<List>());
          expect(body['messages'][0]['role'], equals('system'));
          expect(body['messages'][0]['content'], contains('toxic ex'));
          
          return http.Response(json.encode(mockResponse), 200);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        final messages = [
          {'role': 'user', 'content': 'Hello'}
        ];

        // Act
        final result = await service.sendChatRequest(messages);

        // Assert
        expect(result, equals('Oh so NOW you want to talk to me???'));
      });

      test('should handle empty response choices', () async {
        // Arrange
        final mockResponse = {'choices': []};
        
        mockClient = MockHttpClient((request) {
          return http.Response(json.encode(mockResponse), 200);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act & Assert
        expect(
          () => service.sendChatRequest([]),
          throwsA(isA<OpenAIException>().having(
            (e) => e.message,
            'message',
            contains('No response choices received'),
          )),
        );
      });

      test('should handle missing message in response', () async {
        // Arrange
        final mockResponse = {
          'choices': [{}]
        };
        
        mockClient = MockHttpClient((request) {
          return http.Response(json.encode(mockResponse), 200);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act & Assert
        expect(
          () => service.sendChatRequest([]),
          throwsA(isA<OpenAIException>().having(
            (e) => e.message,
            'message',
            contains('No message found'),
          )),
        );
      });

      test('should handle empty content in response', () async {
        // Arrange
        final mockResponse = {
          'choices': [
            {
              'message': {
                'role': 'assistant',
                'content': ''
              }
            }
          ]
        };
        
        mockClient = MockHttpClient((request) {
          return http.Response(json.encode(mockResponse), 200);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act & Assert
        expect(
          () => service.sendChatRequest([]),
          throwsA(isA<OpenAIException>().having(
            (e) => e.message,
            'message',
            contains('Empty response received'),
          )),
        );
      });

      test('should handle 401 unauthorized error', () async {
        // Arrange
        final errorResponse = {
          'error': {
            'message': 'Invalid API key provided',
            'type': 'invalid_request_error'
          }
        };
        
        mockClient = MockHttpClient((request) {
          return http.Response(json.encode(errorResponse), 401);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act & Assert
        expect(
          () => service.sendChatRequest([]),
          throwsA(isA<OpenAIException>().having(
            (e) => e.message,
            'message',
            contains('Invalid API key'),
          )),
        );
      });

      test('should handle 429 rate limit error', () async {
        // Arrange
        final errorResponse = {
          'error': {
            'message': 'Rate limit reached',
            'type': 'rate_limit_error'
          }
        };
        
        mockClient = MockHttpClient((request) {
          return http.Response(json.encode(errorResponse), 429);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act & Assert
        expect(
          () => service.sendChatRequest([]),
          throwsA(isA<OpenAIException>().having(
            (e) => e.message,
            'message',
            contains('Rate limit exceeded'),
          )),
        );
      });

      test('should handle 500 server error', () async {
        // Arrange
        final errorResponse = {
          'error': {
            'message': 'Internal server error',
            'type': 'server_error'
          }
        };
        
        mockClient = MockHttpClient((request) {
          return http.Response(json.encode(errorResponse), 500);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act & Assert
        expect(
          () => service.sendChatRequest([]),
          throwsA(isA<OpenAIException>().having(
            (e) => e.message,
            'message',
            contains('OpenAI server error'),
          )),
        );
      });

      test('should handle malformed JSON response', () async {
        // Arrange
        mockClient = MockHttpClient((request) {
          return http.Response('invalid json', 200);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act & Assert
        expect(
          () => service.sendChatRequest([]),
          throwsA(isA<OpenAIException>().having(
            (e) => e.message,
            'message',
            contains('Failed to parse OpenAI response'),
          )),
        );
      });

      test('should handle network timeout', () async {
        // Arrange
        mockClient = MockHttpClient((request) {
          // This will cause the timeout to trigger since we're not returning immediately
          throw Exception('This should timeout before this exception is reached');
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act & Assert
        expect(
          () => service.sendChatRequest([]),
          throwsA(isA<OpenAIException>().having(
            (e) => e.message,
            'message',
            contains('Failed to get AI response'),
          )),
        );
      });
    });

    group('system prompt', () {
      test('should include toxic ex personality traits', () async {
        // Arrange
        String? capturedSystemPrompt;
        
        mockClient = MockHttpClient((request) {
          final body = json.decode((request as http.Request).body);
          capturedSystemPrompt = body['messages'][0]['content'];
          
          final mockResponse = {
            'choices': [
              {
                'message': {
                  'role': 'assistant',
                  'content': 'Test response'
                }
              }
            ]
          };
          return http.Response(json.encode(mockResponse), 200);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act
        await service.sendChatRequest([{'role': 'user', 'content': 'test'}]);

        // Assert
        expect(capturedSystemPrompt, isNotNull);
        expect(capturedSystemPrompt!, contains('toxic ex'));
        expect(capturedSystemPrompt!, contains('dramatic'));
        expect(capturedSystemPrompt!, contains('clingy'));
        expect(capturedSystemPrompt!, contains('passive-aggressive'));
        expect(capturedSystemPrompt!, contains('emojis'));
        expect(capturedSystemPrompt!, contains('entertaining'));
      });
    });

    group('request configuration', () {
      test('should use correct model and parameters', () async {
        // Arrange
        Map<String, dynamic>? capturedBody;
        
        mockClient = MockHttpClient((request) {
          capturedBody = json.decode((request as http.Request).body);
          
          final mockResponse = {
            'choices': [
              {
                'message': {
                  'role': 'assistant',
                  'content': 'Test response'
                }
              }
            ]
          };
          return http.Response(json.encode(mockResponse), 200);
        });

        service = OpenAIService(httpClient: mockClient, apiKey: testApiKey);

        // Act
        await service.sendChatRequest([{'role': 'user', 'content': 'test'}]);

        // Assert
        expect(capturedBody, isNotNull);
        expect(capturedBody!['model'], equals('gpt-3.5-turbo'));
        expect(capturedBody!['max_tokens'], equals(150));
        expect(capturedBody!['temperature'], equals(0.9));
        expect(capturedBody!['presence_penalty'], equals(0.6));
        expect(capturedBody!['frequency_penalty'], equals(0.3));
      });
    });
  });

  group('OpenAIException', () {
    test('should create exception with message', () {
      const exception = OpenAIException('Test error');
      expect(exception.message, equals('Test error'));
      expect(exception.toString(), equals('OpenAIException: Test error'));
    });
  });
}