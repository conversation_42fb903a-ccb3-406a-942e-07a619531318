import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/screens/chat_screen.dart';
import 'package:toxic_chat/services/language_detection_service.dart';

void main() {
  group('Language Detection Integration Tests', () {
    testWidgets('Language detection service is integrated in ChatScreen', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChatScreen(),
        ),
      );

      // Verify the screen loads without errors
      expect(find.byType(ChatScreen), findsOneWidget);
      
      // Verify language indicator is present in the app bar
      expect(find.byIcon(Icons.language), findsOneWidget);
      
      // Verify default language is English
      expect(find.text('English'), findsOneWidget);
    });

    test('Language detection service detects Spanish correctly', () async {
      final service = LanguageDetectionService();
      
      final result = await service.detectLanguage('Hola, ¿cómo estás?');
      expect(result, equals('es'));
      
      final languageName = service.getLanguageName(result);
      expect(languageName, equals('Spanish'));
    });

    test('Language detection service detects English correctly', () async {
      final service = LanguageDetectionService();
      
      final result = await service.detectLanguage('Hello, how are you?');
      expect(result, equals('en'));
      
      final languageName = service.getLanguageName(result);
      expect(languageName, equals('English'));
    });

    test('Language detection service detects French correctly', () async {
      final service = LanguageDetectionService();
      
      final result = await service.detectLanguage('Bonjour, comment allez-vous?');
      expect(result, equals('fr'));
      
      final languageName = service.getLanguageName(result);
      expect(languageName, equals('French'));
    });

    test('Language detection service handles empty text', () async {
      final service = LanguageDetectionService();
      
      final result = await service.detectLanguage('');
      expect(result, equals('en')); // Should default to English
    });

    test('Language detection service supports all required languages', () {
      final service = LanguageDetectionService();
      
      expect(service.isLanguageSupported('en'), isTrue);
      expect(service.isLanguageSupported('es'), isTrue);
      expect(service.isLanguageSupported('fr'), isTrue);
      expect(service.isLanguageSupported('pt'), isTrue);
      expect(service.isLanguageSupported('it'), isTrue);
      expect(service.isLanguageSupported('de'), isFalse); // German not supported
    });
  });
}