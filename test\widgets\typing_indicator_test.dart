import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/widgets/typing_indicator.dart';

void main() {
  group('TypingIndicator Widget Tests', () {
    testWidgets('should not render when isVisible is false', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: TypingIndicator(isVisible: false)),
        ),
      );

      expect(find.byType(TypingIndicator), findsOneWidget);
    });

    testWidgets('should render when isVisible is true', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: TypingIndicator(isVisible: true)),
        ),
      );

      // Allow animations to start
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));

      expect(find.byType(TypingIndicator), findsOneWidget);
    });

    testWidgets('should show animated dots when visible', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: TypingIndicator(isVisible: true)),
        ),
      );

      // Allow animations to start
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));

      // Check that dots are present (they are Container widgets with circular decoration)
      final dotContainers = find.byWidgetPredicate(
        (widget) =>
            widget is Container &&
            widget.decoration is BoxDecoration &&
            (widget.decoration as BoxDecoration).shape == BoxShape.circle,
      );

      expect(dotContainers, findsNWidgets(3)); // Should have 3 dots
    });

    testWidgets('should animate visibility changes', (
      WidgetTester tester,
    ) async {
      bool isVisible = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    TypingIndicator(isVisible: isVisible),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          isVisible = !isVisible;
                        });
                      },
                      child: const Text('Toggle'),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      );

      // Initially not visible
      expect(find.byType(TypingIndicator), findsOneWidget);

      // Tap to make visible
      await tester.tap(find.text('Toggle'));
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));

      // Should now be visible
      expect(find.byType(TypingIndicator), findsOneWidget);

      // Tap to hide
      await tester.tap(find.text('Toggle'));
      await tester.pump();
      await tester.pump(
        const Duration(milliseconds: 400),
      ); // Wait for animation

      // Should be hidden again
      expect(find.byType(TypingIndicator), findsOneWidget);
    });

    testWidgets('should use custom animation duration when provided', (
      WidgetTester tester,
    ) async {
      const customDuration = Duration(milliseconds: 500);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: TypingIndicator(
              isVisible: true,
              animationDuration: customDuration,
            ),
          ),
        ),
      );

      // The widget should accept the custom duration without errors
      expect(find.byType(TypingIndicator), findsOneWidget);
    });
  });
}
