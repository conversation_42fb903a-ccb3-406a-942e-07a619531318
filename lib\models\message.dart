import 'chat_bubble_style.dart';
import 'personality_mode.dart';

class Message {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? role; // 'user', 'assistant', 'system'
  final String? language; // Language of the message (e.g., 'english', 'spanish')
  final PersonalityMode? personality; // AI personality mode used for this message

  Message({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.role,
    this.language,
    this.personality,
  });

  /// Convert Message to JSON format
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'isUser': isUser,
      'timestamp': timestamp.toIso8601String(),
      'role': role,
      'language': language,
      'personality': personality?.value,
    };
  }

  /// Create Message from JSON
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      text: json['text'] as String,
      isUser: json['isUser'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
      role: json['role'] as String?,
      language: json['language'] as String?,
      personality: json['personality'] != null 
          ? PersonalityModeExtension.fromString(json['personality'] as String)
          : null,
    );
  }

  /// Convert Message to OpenAI API format
  Map<String, dynamic> toOpenAIFormat() {
    return {
      'role': role ?? (isUser ? 'user' : 'assistant'),
      'content': text,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message &&
        other.text == text &&
        other.isUser == isUser &&
        other.timestamp == timestamp &&
        other.role == role &&
        other.language == language &&
        other.personality == personality;
  }

  @override
  int get hashCode {
    return text.hashCode ^
        isUser.hashCode ^
        timestamp.hashCode ^
        role.hashCode ^
        language.hashCode ^
        personality.hashCode;
  }

  /// Get formatted timestamp for display
  String getFormattedTimestamp() {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      // Format as MM/dd for older messages
      return '${timestamp.month.toString().padLeft(2, '0')}/${timestamp.day.toString().padLeft(2, '0')}';
    }
  }

  /// Check if this message should be grouped with the previous message
  bool shouldGroupWithPrevious(Message? previousMessage) {
    if (previousMessage == null) return false;
    
    // Group if same sender and within 2 minutes
    final timeDifference = timestamp.difference(previousMessage.timestamp);
    return isUser == previousMessage.isUser && 
           timeDifference.inMinutes < 2;
  }

  /// Check if this message should be grouped with the next message
  bool shouldGroupWithNext(Message? nextMessage) {
    if (nextMessage == null) return false;
    return nextMessage.shouldGroupWithPrevious(this);
  }

  /// Get the position of this message in a group (first, middle, last, single)
  MessageGroupPosition getGroupPosition(Message? previousMessage, Message? nextMessage) {
    final groupWithPrevious = shouldGroupWithPrevious(previousMessage);
    final groupWithNext = shouldGroupWithNext(nextMessage);
    
    if (groupWithPrevious && groupWithNext) {
      return MessageGroupPosition.middle;
    } else if (groupWithPrevious && !groupWithNext) {
      return MessageGroupPosition.last;
    } else if (!groupWithPrevious && groupWithNext) {
      return MessageGroupPosition.first;
    } else {
      return MessageGroupPosition.single;
    }
  }

  /// Get chat bubble alignment based on sender
  ChatBubbleAlignment getAlignment() {
    return ChatBubbleLayoutHelper.getAlignment(isUser);
  }

  @override
  String toString() {
    return 'Message(text: $text, isUser: $isUser, timestamp: $timestamp, role: $role, language: $language, personality: $personality)';
  }
}