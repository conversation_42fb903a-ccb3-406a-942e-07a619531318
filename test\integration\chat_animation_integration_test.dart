import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:toxic_chat/providers/theme_provider.dart';
import 'package:toxic_chat/screens/chat_screen.dart';
import 'package:toxic_chat/widgets/message_bubble.dart';
import 'package:toxic_chat/widgets/message_input.dart';
import 'package:toxic_chat/widgets/typing_indicator.dart';

void main() {
  group('Chat Animation Integration Tests', () {
    testWidgets('Complete message animation flow', (WidgetTester tester) async {
      // Build the chat screen
      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const MaterialApp(home: ChatScreen()),
        ),
      );

      // Verify initial state
      expect(find.byType(MessageInput), findsOneWidget);
      expect(find.byType(MessageBubble), findsNothing);
      expect(find.text('Start a conversation'), findsOneWidget);

      // Note: Since we can't easily mock the backend service in this test,
      // we'll focus on testing the UI components and their animations

      // Verify that the message input is present and functional
      final messageInput = find.byType(TextField);
      expect(messageInput, findsOneWidget);

      // Verify send button is present
      final sendButton = find.byIcon(Icons.send_outlined);
      expect(sendButton, findsOneWidget);

      // Test typing in the input field
      await tester.enterText(messageInput, 'Test message');
      await tester.pump();

      // Send button should now be enabled (colored)
      expect(find.byIcon(Icons.send_rounded), findsOneWidget);
    });

    testWidgets('Animation controllers are properly managed', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const MaterialApp(home: ChatScreen()),
        ),
      );

      // The ChatScreen should be using TickerProviderStateMixin
      final chatScreenState = tester.state<State<ChatScreen>>(
        find.byType(ChatScreen),
      );
      expect(chatScreenState, isA<TickerProviderStateMixin>());

      // Dispose should not throw errors
      await tester.pumpWidget(const MaterialApp(home: Scaffold()));

      // No exceptions should be thrown during disposal
    });

    testWidgets('Typing indicator animation works', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: TypingIndicator(isVisible: true)),
        ),
      );

      // Typing indicator should be visible
      expect(find.byType(TypingIndicator), findsOneWidget);

      // Should have animated dots
      await tester.pump(const Duration(milliseconds: 100));
      await tester.pump(const Duration(milliseconds: 200));
      await tester.pump(const Duration(milliseconds: 300));

      // Indicator should still be present after animations
      expect(find.byType(TypingIndicator), findsOneWidget);
    });

    testWidgets('Message input animations work correctly', (
      WidgetTester tester,
    ) async {
      bool messageSent = false;
      String sentMessage = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {
                messageSent = true;
                sentMessage = message;
              },
              isLoading: false,
            ),
          ),
        ),
      );

      // Find the text field and send button
      final textField = find.byType(TextField);
      final sendButton = find.byIcon(Icons.send_outlined);

      expect(textField, findsOneWidget);
      expect(sendButton, findsOneWidget);

      // Type a message
      await tester.enterText(textField, 'Hello world');
      await tester.pump();

      // Tap the send button
      await tester.tap(find.byIcon(Icons.send_rounded));
      await tester.pump();

      // Verify the message was sent
      expect(messageSent, isTrue);
      expect(sentMessage, equals('Hello world'));

      // The input should be cleared
      expect(find.text('Hello world'), findsNothing);
    });

    testWidgets('Scroll animation works with messages', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const MaterialApp(home: ChatScreen()),
        ),
      );

      // Find the ListView (should exist even when empty)
      final listView = find.byType(ListView);

      // In empty state, we should see the empty state widget instead
      expect(find.text('Start a conversation'), findsOneWidget);

      // The scroll controller should be properly initialized
      final chatScreenState = tester.state(find.byType(ChatScreen));
      expect(chatScreenState, isNotNull);
    });

    group('Cross-platform Animation Tests', () {
      testWidgets('Animations work on different screen sizes', (
        WidgetTester tester,
      ) async {
        // Test with different screen sizes to simulate different platforms

        // Mobile portrait
        tester.view.physicalSize = const Size(400, 800);
        tester.view.devicePixelRatio = 2.0;

        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => ThemeProvider(),
            child: const MaterialApp(home: ChatScreen()),
          ),
        );

        expect(find.byType(ChatScreen), findsOneWidget);

        // Tablet landscape
        tester.view.physicalSize = const Size(1024, 768);
        tester.view.devicePixelRatio = 1.0;

        await tester.pump();
        expect(find.byType(ChatScreen), findsOneWidget);

        // Reset to default
        addTearDown(tester.view.reset);
      });

      testWidgets('Animations handle rapid state changes', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => ThemeProvider(),
            child: const MaterialApp(home: ChatScreen()),
          ),
        );

        // Rapidly change states to test animation stability
        for (int i = 0; i < 5; i++) {
          await tester.pump(const Duration(milliseconds: 50));
        }

        // Should not crash or throw exceptions
        expect(find.byType(ChatScreen), findsOneWidget);
      });
    });

    group('Performance Tests', () {
      testWidgets('Animation controllers are disposed properly', (
        WidgetTester tester,
      ) async {
        // Create and dispose the chat screen multiple times
        for (int i = 0; i < 3; i++) {
          await tester.pumpWidget(
            ChangeNotifierProvider(
              create: (_) => ThemeProvider(),
              child: const MaterialApp(home: ChatScreen()),
            ),
          );

          await tester.pump();

          await tester.pumpWidget(
            const MaterialApp(home: Scaffold(body: Text('Empty'))),
          );

          await tester.pump();
        }

        // Should not have memory leaks or disposal errors
        expect(find.text('Empty'), findsOneWidget);
      });
    });
  });
}
