import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import '../../lib/services/chat_service.dart';
import '../../lib/services/openai_service.dart';
import '../../lib/models/message.dart';
import '../../lib/models/personality_mode.dart';

@GenerateMocks([OpenAIService])
import 'enhanced_chat_service_test.mocks.dart';

void main() {
  group('ChatService Enhanced Features', () {
    late ChatService chatService;
    late MockOpenAIService mockOpenAIService;

    setUp(() {
      mockOpenAIService = MockOpenAIService();
      chatService = ChatService(openAiService: mockOpenAIService);
    });

    tearDown(() {
      chatService.dispose();
    });

    group('sendMessage with personality and language', () {
      test('should pass personality and language to OpenAI service', () async {
        // Arrange
        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
          ),
        ];

        when(mockOpenAIService.sendChatRequest(
          any,
          personality: anyNamed('personality'),
          language: anyNamed('language'),
        )).thenAnswer((_) async => 'Hola, ahora me hablas?');

        // Act
        final result = await chatService.sendMessage(
          messages,
          personality: PersonalityMode.toxic,
          language: 'spanish',
        );

        // Assert
        expect(result.text, 'Hola, ahora me hablas?');
        expect(result.isUser, false);
        expect(result.role, 'assistant');
        expect(result.language, 'spanish');
        expect(result.personality, PersonalityMode.toxic);

        // Verify the OpenAI service was called with correct parameters
        verify(mockOpenAIService.sendChatRequest(
          any,
          personality: PersonalityMode.toxic,
          language: 'spanish',
        )).called(1);
      });

      test('should use default personality and language when not specified', () async {
        // Arrange
        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
          ),
        ];

        when(mockOpenAIService.sendChatRequest(
          any,
          personality: anyNamed('personality'),
          language: anyNamed('language'),
        )).thenAnswer((_) async => 'Oh so NOW you talk to me?');

        // Act
        final result = await chatService.sendMessage(messages);

        // Assert
        expect(result.text, 'Oh so NOW you talk to me?');
        expect(result.language, 'english');
        expect(result.personality, PersonalityMode.toxic);

        // Verify the OpenAI service was called with default parameters
        verify(mockOpenAIService.sendChatRequest(
          any,
          personality: PersonalityMode.toxic,
          language: 'english',
        )).called(1);
      });

      test('should handle different personality modes correctly', () async {
        // Test cases for different personalities
        final testCases = [
          (PersonalityMode.witch, 'The stars told me you would come...'),
          (PersonalityMode.jealous, 'Who were you talking to before?'),
          (PersonalityMode.dramatic, 'FINALLY!!! I thought you forgot me!'),
          (PersonalityMode.sarcastic, 'Oh, what a surprise...'),
        ];

        for (final testCase in testCases) {
          final personality = testCase.$1;
          final expectedResponse = testCase.$2;

          // Arrange
          final messages = [
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
            ),
          ];

          when(mockOpenAIService.sendChatRequest(
            any,
            personality: personality,
            language: anyNamed('language'),
          )).thenAnswer((_) async => expectedResponse);

          // Act
          final result = await chatService.sendMessage(
            messages,
            personality: personality,
          );

          // Assert
          expect(result.text, expectedResponse);
          expect(result.personality, personality);

          // Verify the OpenAI service was called with correct personality
          verify(mockOpenAIService.sendChatRequest(
            any,
            personality: personality,
            language: 'english',
          )).called(1);

          // Reset mock for next iteration
          reset(mockOpenAIService);
        }
      });

      test('should handle different languages correctly', () async {
        // Test cases for different languages
        final testCases = [
          ('spanish', 'Hola, ahora me hablas?'),
          ('french', 'Ah, maintenant tu me parles?'),
          ('portuguese', 'Ah, agora você fala comigo?'),
          ('italian', 'Ah, ora mi parli?'),
          ('english', 'Oh so NOW you talk to me?'),
        ];

        for (final testCase in testCases) {
          final language = testCase.$1;
          final expectedResponse = testCase.$2;

          // Arrange
          final messages = [
            Message(
              text: 'Hello',
              isUser: true,
              timestamp: DateTime.now(),
            ),
          ];

          when(mockOpenAIService.sendChatRequest(
            any,
            personality: anyNamed('personality'),
            language: language,
          )).thenAnswer((_) async => expectedResponse);

          // Act
          final result = await chatService.sendMessage(
            messages,
            language: language,
          );

          // Assert
          expect(result.text, expectedResponse);
          expect(result.language, language);

          // Verify the OpenAI service was called with correct language
          verify(mockOpenAIService.sendChatRequest(
            any,
            personality: PersonalityMode.toxic,
            language: language,
          )).called(1);

          // Reset mock for next iteration
          reset(mockOpenAIService);
        }
      });

      test('should convert messages to correct OpenAI format', () async {
        // Arrange
        final messages = [
          Message(
            text: 'First message',
            isUser: true,
            timestamp: DateTime.now(),
          ),
          Message(
            text: 'AI response',
            isUser: false,
            timestamp: DateTime.now(),
            role: 'assistant',
          ),
          Message(
            text: 'Second user message',
            isUser: true,
            timestamp: DateTime.now(),
          ),
        ];

        when(mockOpenAIService.sendChatRequest(
          any,
          personality: anyNamed('personality'),
          language: anyNamed('language'),
        )).thenAnswer((_) async => 'Response');

        // Act
        await chatService.sendMessage(messages);

        // Assert
        final captured = verify(mockOpenAIService.sendChatRequest(
          captureAny,
          personality: anyNamed('personality'),
          language: anyNamed('language'),
        )).captured;

        final openAiMessages = captured.first as List<Map<String, dynamic>>;
        expect(openAiMessages.length, 3);
        expect(openAiMessages[0]['role'], 'user');
        expect(openAiMessages[0]['content'], 'First message');
        expect(openAiMessages[1]['role'], 'assistant');
        expect(openAiMessages[1]['content'], 'AI response');
        expect(openAiMessages[2]['role'], 'user');
        expect(openAiMessages[2]['content'], 'Second user message');
      });

      test('should handle OpenAI service errors correctly', () async {
        // Arrange
        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
          ),
        ];

        when(mockOpenAIService.sendChatRequest(
          any,
          personality: anyNamed('personality'),
          language: anyNamed('language'),
        )).thenThrow(const OpenAIException('API error'));

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>()),
        );
      });

      test('should validate that messages list is not empty', () async {
        // Act & Assert
        expect(
          () => chatService.sendMessage([]),
          throwsA(isA<ChatServiceException>()),
        );
      });

      test('should validate that last message is from user', () async {
        // Arrange
        final messages = [
          Message(
            text: 'AI message',
            isUser: false,
            timestamp: DateTime.now(),
            role: 'assistant',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>()),
        );
      });
    });
  });
}