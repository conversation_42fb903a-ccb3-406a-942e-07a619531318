import 'personality_mode.dart';

/// Configuration class for managing personality settings
class PersonalityConfiguration {
  final PersonalityMode currentPersonality;
  final bool enableEmotionalResponses;
  final double emotionalIntensity; // 0.0 to 1.0
  final String preferredLanguage;
  final bool autoDetectLanguage;
  final Map<String, dynamic> customSettings;

  const PersonalityConfiguration({
    this.currentPersonality = PersonalityMode.toxic,
    this.enableEmotionalResponses = true,
    this.emotionalIntensity = 0.8,
    this.preferredLanguage = 'english',
    this.autoDetectLanguage = true,
    this.customSettings = const {},
  });

  /// Creates a copy of this configuration with updated values
  PersonalityConfiguration copyWith({
    PersonalityMode? currentPersonality,
    bool? enableEmotionalResponses,
    double? emotionalIntensity,
    String? preferredLanguage,
    bool? autoDetectLanguage,
    Map<String, dynamic>? customSettings,
  }) {
    return PersonalityConfiguration(
      currentPersonality: currentPersonality ?? this.currentPersonality,
      enableEmotionalResponses: enableEmotionalResponses ?? this.enableEmotionalResponses,
      emotionalIntensity: emotionalIntensity ?? this.emotionalIntensity,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      autoDetectLanguage: autoDetectLanguage ?? this.autoDetectLanguage,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  /// Converts the configuration to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'currentPersonality': currentPersonality.value,
      'enableEmotionalResponses': enableEmotionalResponses,
      'emotionalIntensity': emotionalIntensity,
      'preferredLanguage': preferredLanguage,
      'autoDetectLanguage': autoDetectLanguage,
      'customSettings': customSettings,
    };
  }

  /// Creates a configuration from a JSON map
  factory PersonalityConfiguration.fromJson(Map<String, dynamic> json) {
    return PersonalityConfiguration(
      currentPersonality: PersonalityModeExtension.fromString(
        json['currentPersonality'] ?? 'toxic',
      ),
      enableEmotionalResponses: json['enableEmotionalResponses'] ?? true,
      emotionalIntensity: (json['emotionalIntensity'] ?? 0.8).toDouble(),
      preferredLanguage: json['preferredLanguage'] ?? 'english',
      autoDetectLanguage: json['autoDetectLanguage'] ?? true,
      customSettings: json['customSettings'] ?? {},
    );
  }

  /// Returns the default configuration
  factory PersonalityConfiguration.defaultConfig() {
    return const PersonalityConfiguration();
  }

  /// Validates the configuration values
  bool isValid() {
    return emotionalIntensity >= 0.0 && 
           emotionalIntensity <= 1.0 &&
           preferredLanguage.isNotEmpty;
  }

  /// Gets a description of the current configuration
  String getDescription() {
    final intensityDesc = emotionalIntensity > 0.7 
        ? 'High' 
        : emotionalIntensity > 0.4 
            ? 'Medium' 
            : 'Low';
    
    return '${currentPersonality.displayName} personality with $intensityDesc emotional intensity';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is PersonalityConfiguration &&
        other.currentPersonality == currentPersonality &&
        other.enableEmotionalResponses == enableEmotionalResponses &&
        other.emotionalIntensity == emotionalIntensity &&
        other.preferredLanguage == preferredLanguage &&
        other.autoDetectLanguage == autoDetectLanguage;
  }

  @override
  int get hashCode {
    return Object.hash(
      currentPersonality,
      enableEmotionalResponses,
      emotionalIntensity,
      preferredLanguage,
      autoDetectLanguage,
    );
  }

  @override
  String toString() {
    return 'PersonalityConfiguration('
        'personality: ${currentPersonality.displayName}, '
        'emotional: $enableEmotionalResponses, '
        'intensity: $emotionalIntensity, '
        'language: $preferredLanguage, '
        'autoDetect: $autoDetectLanguage'
        ')';
  }
}