import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:toxic_chat/screens/chat_screen.dart';
import 'package:toxic_chat/widgets/message_bubble.dart';
import 'package:toxic_chat/widgets/message_input.dart';
import 'package:toxic_chat/widgets/typing_indicator.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/services/chat_service.dart';

void main() {
  group('Real-time Messaging Integration Tests', () {
    testWidgets('ChatScreen has all required components for real-time messaging', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ChatScreen(),
        ),
      );

      // Wait for initial state
      await tester.pump();

      // Verify initial empty state shows
      expect(find.text('Start a conversation'), findsOneWidget);
      expect(find.byType(MessageBubble), findsNothing);

      // Verify message input components are present
      final messageInput = find.byType(TextField);
      final sendButton = find.byIcon(Icons.send_rounded);

      expect(messageInput, findsOneWidget);
      expect(sendButton, findsOneWidget);

      // Verify the input field accepts text
      await tester.enterText(messageInput, 'Test message');
      await tester.pump();

      // Verify text was entered
      final textField = tester.widget<TextField>(messageInput);
      expect(textField.controller?.text, equals('Test message'));
    });

    testWidgets('ChatService integration is properly implemented', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ChatScreen(),
        ),
      );

      await tester.pump();

      // Verify ChatService is integrated by checking service availability indicator
      expect(find.byIcon(Icons.cloud_off), findsOneWidget); // Service unavailable initially
      
      // Verify error handling is in place by checking for service status
      final serviceIcon = find.byIcon(Icons.cloud_off);
      expect(serviceIcon, findsOneWidget);
      
      // Tap the service icon to trigger availability check
      await tester.tap(serviceIcon);
      await tester.pump();
      
      // The service check should complete without errors
      expect(find.byType(ChatScreen), findsOneWidget);
    });

    testWidgets('Message input clears after sending', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ChatScreen(),
        ),
      );

      await tester.pump();

      // Type a message
      final messageInput = find.byType(TextField);
      await tester.enterText(messageInput, 'Test message');
      await tester.pump();

      // Verify text is there
      final textFieldBefore = tester.widget<TextField>(messageInput);
      expect(textFieldBefore.controller?.text, equals('Test message'));

      // Tap send button (this will try to send but fail due to no backend)
      await tester.tap(find.byIcon(Icons.send_rounded));
      await tester.pump();

      // Wait for any pending timers to complete
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Input should be cleared even if send fails
      final textFieldAfter = tester.widget<TextField>(messageInput);
      expect(textFieldAfter.controller?.text, isEmpty);
    });

    testWidgets('Loading state management is implemented', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ChatScreen(),
        ),
      );

      await tester.pump();

      // Send a message to trigger loading state
      await tester.enterText(find.byType(TextField), 'Test message');
      await tester.tap(find.byIcon(Icons.send_rounded));
      await tester.pump();

      // Should show some kind of loading indication or error handling
      // Since backend is not available, we should see an error snackbar eventually
      await tester.pump(const Duration(milliseconds: 100));
      
      // The app should handle the error gracefully without crashing
      expect(find.byType(ChatScreen), findsOneWidget);
    });

    testWidgets('Message history structure is maintained', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ChatScreen(),
        ),
      );

      await tester.pump();

      // In empty state, ListView is not shown - instead empty state is shown
      expect(find.text('Start a conversation'), findsOneWidget);
      expect(find.byType(ListView), findsNothing);
      
      // Verify that the screen has proper structure for message history
      // The Column widget should be there to contain the layout
      expect(find.byType(Column), findsAtLeastNWidgets(1));
      
      // Verify ChatScreen is properly initialized
      expect(find.byType(ChatScreen), findsOneWidget);
    });

    testWidgets('Animation controllers are properly managed', (WidgetTester tester) async {
      // Test that the ChatScreen can be created and disposed without memory leaks
      for (int i = 0; i < 3; i++) {
        await tester.pumpWidget(
          const MaterialApp(
            home: ChatScreen(),
          ),
        );

        await tester.pump();

        // Verify the screen is working
        expect(find.byType(ChatScreen), findsOneWidget);

        // Dispose by navigating away
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(body: Text('Empty')),
          ),
        );

        await tester.pump();
      }

      // Should not have memory leaks or disposal errors
      expect(find.text('Empty'), findsOneWidget);
    });

  });
}