import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/message.dart';

void main() {
  group('Timestamp Formatting Tests', () {
    test('should format recent timestamps correctly', () {
      final now = DateTime.now();
      
      // Just now (less than 1 minute)
      final recentMessage = Message(
        text: 'Recent message',
        isUser: true,
        timestamp: now.subtract(const Duration(seconds: 30)),
      );
      expect(recentMessage.getFormattedTimestamp(), equals('Just now'));
      
      // Minutes ago
      final minutesMessage = Message(
        text: 'Minutes message',
        isUser: true,
        timestamp: now.subtract(const Duration(minutes: 5)),
      );
      expect(minutesMessage.getFormattedTimestamp(), equals('5m ago'));
      
      // Hours ago
      final hoursMessage = Message(
        text: 'Hours message',
        isUser: true,
        timestamp: now.subtract(const Duration(hours: 3)),
      );
      expect(hoursMessage.getFormattedTimestamp(), equals('3h ago'));
      
      // Days ago
      final daysMessage = Message(
        text: 'Days message',
        isUser: true,
        timestamp: now.subtract(const Duration(days: 2)),
      );
      expect(daysMessage.getFormattedTimestamp(), equals('2d ago'));
    });

    test('should format old timestamps with date', () {
      final now = DateTime.now();
      
      // Week old message should show date
      final oldMessage = Message(
        text: 'Old message',
        isUser: true,
        timestamp: now.subtract(const Duration(days: 10)),
      );
      
      final formattedTimestamp = oldMessage.getFormattedTimestamp();
      // Should be in MM/dd format
      expect(formattedTimestamp, matches(r'^\d{2}/\d{2}$'));
    });

    test('should handle edge cases correctly', () {
      final now = DateTime.now();
      
      // Exactly 1 minute
      final oneMinuteMessage = Message(
        text: 'One minute message',
        isUser: true,
        timestamp: now.subtract(const Duration(minutes: 1)),
      );
      expect(oneMinuteMessage.getFormattedTimestamp(), equals('1m ago'));
      
      // Exactly 1 hour
      final oneHourMessage = Message(
        text: 'One hour message',
        isUser: true,
        timestamp: now.subtract(const Duration(hours: 1)),
      );
      expect(oneHourMessage.getFormattedTimestamp(), equals('1h ago'));
      
      // Exactly 1 day
      final oneDayMessage = Message(
        text: 'One day message',
        isUser: true,
        timestamp: now.subtract(const Duration(days: 1)),
      );
      expect(oneDayMessage.getFormattedTimestamp(), equals('1d ago'));
    });
  });
}