import 'package:flutter/material.dart';

/// Utility class for responsive design calculations
class ResponsiveUtils {
  /// Screen size breakpoints
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  /// Get screen type based on width
  static ScreenType getScreenType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return ScreenType.mobile;
    } else if (width < tabletBreakpoint) {
      return ScreenType.tablet;
    } else if (width < desktopBreakpoint) {
      return ScreenType.desktop;
    } else {
      return ScreenType.largeDesktop;
    }
  }

  /// Check if device is mobile
  static bool isMobile(BuildContext context) {
    return getScreenType(context) == ScreenType.mobile;
  }

  /// Check if device is tablet
  static bool isTablet(BuildContext context) {
    return getScreenType(context) == ScreenType.tablet;
  }

  /// Check if device is desktop or larger
  static bool isDesktop(BuildContext context) {
    final screenType = getScreenType(context);
    return screenType == ScreenType.desktop || screenType == ScreenType.largeDesktop;
  }

  /// Get responsive font size based on screen size
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final screenType = getScreenType(context);
    
    double scaleFactor;
    switch (screenType) {
      case ScreenType.mobile:
        scaleFactor = 1.0;
        break;
      case ScreenType.tablet:
        scaleFactor = 1.1;
        break;
      case ScreenType.desktop:
        scaleFactor = 1.2;
        break;
      case ScreenType.largeDesktop:
        scaleFactor = 1.3;
        break;
    }
    
    return baseFontSize * scaleFactor;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context, {
    double mobile = 16.0,
    double tablet = 24.0,
    double desktop = 32.0,
  }) {
    final screenType = getScreenType(context);
    
    double padding;
    switch (screenType) {
      case ScreenType.mobile:
        padding = mobile;
        break;
      case ScreenType.tablet:
        padding = tablet;
        break;
      case ScreenType.desktop:
      case ScreenType.largeDesktop:
        padding = desktop;
        break;
    }
    
    return EdgeInsets.all(padding);
  }

  /// Get responsive horizontal padding
  static EdgeInsets getResponsiveHorizontalPadding(BuildContext context, {
    double mobile = 16.0,
    double tablet = 32.0,
    double desktop = 64.0,
  }) {
    final screenType = getScreenType(context);
    
    double padding;
    switch (screenType) {
      case ScreenType.mobile:
        padding = mobile;
        break;
      case ScreenType.tablet:
        padding = tablet;
        break;
      case ScreenType.desktop:
      case ScreenType.largeDesktop:
        padding = desktop;
        break;
    }
    
    return EdgeInsets.symmetric(horizontal: padding);
  }

  /// Get maximum content width for centering on large screens
  static double getMaxContentWidth(BuildContext context) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return double.infinity;
      case ScreenType.tablet:
        return 700.0;
      case ScreenType.desktop:
        return 800.0;
      case ScreenType.largeDesktop:
        return 900.0;
    }
  }

  /// Get responsive message bubble constraints
  static BoxConstraints getMessageBubbleConstraints(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenType = getScreenType(context);
    
    double maxWidthFactor;
    switch (screenType) {
      case ScreenType.mobile:
        maxWidthFactor = 0.75;
        break;
      case ScreenType.tablet:
        maxWidthFactor = 0.65;
        break;
      case ScreenType.desktop:
      case ScreenType.largeDesktop:
        maxWidthFactor = 0.55;
        break;
    }
    
    final maxWidth = screenWidth * maxWidthFactor;
    final constrainedMaxWidth = maxWidth.clamp(200.0, 600.0);
    
    return BoxConstraints(
      maxWidth: constrainedMaxWidth,
      minWidth: 0.0,
    );
  }

  /// Get responsive spacing based on screen size
  static double getResponsiveSpacing(BuildContext context, {
    double mobile = 8.0,
    double tablet = 12.0,
    double desktop = 16.0,
  }) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet;
      case ScreenType.desktop:
      case ScreenType.largeDesktop:
        return desktop;
    }
  }

  /// Get responsive icon size
  static double getResponsiveIconSize(BuildContext context, double baseSize) {
    final screenType = getScreenType(context);
    
    double scaleFactor;
    switch (screenType) {
      case ScreenType.mobile:
        scaleFactor = 1.0;
        break;
      case ScreenType.tablet:
        scaleFactor = 1.1;
        break;
      case ScreenType.desktop:
        scaleFactor = 1.2;
        break;
      case ScreenType.largeDesktop:
        scaleFactor = 1.3;
        break;
    }
    
    return baseSize * scaleFactor;
  }

  /// Check if device is in landscape mode
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Get safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// Get keyboard height
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }
}

/// Screen size types for responsive design
enum ScreenType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}
