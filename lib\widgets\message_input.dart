import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/responsive_utils.dart';

class MessageInput extends StatefulWidget {
  final Function(String) onSendMessage;
  final bool isLoading;

  const MessageInput({
    super.key,
    required this.onSendMessage,
    this.isLoading = false,
  });

  @override
  State<MessageInput> createState() => _MessageInputState();
}

class _MessageInputState extends State<MessageInput>
    with TickerProviderStateMixin {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  late AnimationController _sendButtonAnimationController;
  late AnimationController _pulseAnimationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<Color?> _buttonColorAnimation;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();

    // Send button press animation
    _sendButtonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.92).animate(
      CurvedAnimation(
        parent: _sendButtonAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Pulse animation for active send button
    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(
        parent: _pulseAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _sendButtonAnimationController.dispose();
    _pulseAnimationController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });

      // Start/stop pulse animation based on text presence
      if (hasText && _isInputValid()) {
        _pulseAnimationController.repeat(reverse: true);
      } else {
        _pulseAnimationController.stop();
        _pulseAnimationController.reset();
      }
    }
  }

  void _onFocusChanged() {
    setState(() {
      // Trigger rebuild to update focus-dependent styling
    });
  }

  void _onSendPressed() async {
    final text = _controller.text.trim();
    if (text.isEmpty || widget.isLoading) return;

    // Haptic feedback for better mobile experience
    HapticFeedback.lightImpact();

    // Animate button press
    _sendButtonAnimationController.forward().then((_) {
      _sendButtonAnimationController.reverse();
    });

    // Stop pulse animation
    _pulseAnimationController.stop();
    _pulseAnimationController.reset();

    // Send message and clear input
    widget.onSendMessage(text);
    _controller.clear();
    setState(() {
      _hasText = false;
    });
  }

  bool _isInputValid() {
    final text = _controller.text.trim();
    return text.isNotEmpty && text.length <= 1000; // Message length limit
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final mediaQuery = MediaQuery.of(context);
    final isKeyboardVisible = mediaQuery.viewInsets.bottom > 0;

    // Responsive values optimized for mobile messaging experience
    final isMobile = ResponsiveUtils.isMobile(context);
    final horizontalPadding = isMobile ? 12.0 : 16.0;
    final verticalPadding = isMobile ? 8.0 : 12.0;
    final inputBorderRadius = isMobile ? 24.0 : 28.0;
    final buttonSize = isMobile ? 48.0 : 56.0;
    final buttonSpacing = isMobile ? 8.0 : 12.0;

    return Container(
      // Enhanced mobile-style background with blur effect
      decoration: BoxDecoration(
        color: colorScheme.surface.withValues(alpha: 0.95),
        border: Border(
          top: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.1),
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10.0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: EdgeInsets.fromLTRB(
            horizontalPadding,
            verticalPadding,
            horizontalPadding,
            // Add extra bottom padding when keyboard is visible for better UX
            isKeyboardVisible ? verticalPadding + 4.0 : verticalPadding,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Enhanced input field with WhatsApp-style design
              Expanded(
                child: Container(
                  constraints: BoxConstraints(
                    minHeight: buttonSize,
                    maxHeight: isMobile ? 120.0 : 160.0,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(inputBorderRadius),
                    color: colorScheme.surfaceContainerHighest,
                    border: Border.all(
                      color: _focusNode.hasFocus
                          ? colorScheme.primary.withValues(alpha: 0.4)
                          : colorScheme.outline.withValues(alpha: 0.2),
                      width: _focusNode.hasFocus ? 1.5 : 1.0,
                    ),
                    boxShadow: [
                      if (_focusNode.hasFocus) ...[
                        BoxShadow(
                          color: colorScheme.primary.withValues(alpha: 0.1),
                          blurRadius: 8.0,
                          offset: const Offset(0, 2),
                        ),
                      ] else ...[
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.04),
                          blurRadius: 4.0,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ],
                  ),
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    enabled: !widget.isLoading,
                    maxLines: isMobile ? 4 : 6,
                    minLines: 1,
                    maxLength: 1000,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _onSendPressed(),
                    textCapitalization: TextCapitalization.sentences,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontSize: isMobile ? 16.0 : 17.0,
                      color: colorScheme.onSurface,
                      height: 1.4,
                    ),
                    decoration: InputDecoration(
                      hintText: widget.isLoading
                          ? 'Sending...'
                          : 'Type a message...',
                      hintStyle: theme.textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurfaceVariant.withValues(
                          alpha: 0.6,
                        ),
                        fontSize: isMobile ? 16.0 : 17.0,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: isMobile ? 16.0 : 20.0,
                        vertical: isMobile ? 12.0 : 16.0,
                      ),
                      counterText: '', // Hide character counter
                      isDense: true,
                    ),
                  ),
                ),
              ),

              SizedBox(width: buttonSpacing),

              // Enhanced send button with better visual feedback
              AnimatedBuilder(
                animation: Listenable.merge([_scaleAnimation, _pulseAnimation]),
                builder: (context, child) {
                  final isActive =
                      _hasText && _isInputValid() && !widget.isLoading;
                  final pulseScale = isActive ? _pulseAnimation.value : 1.0;

                  return Transform.scale(
                    scale: _scaleAnimation.value * pulseScale,
                    child: Container(
                      width: buttonSize,
                      height: buttonSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: isActive
                            ? LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  const Color(0xFF25D366), // WhatsApp green
                                  const Color(
                                    0xFF128C7E,
                                  ), // Darker WhatsApp green
                                ],
                              )
                            : LinearGradient(
                                colors: [
                                  colorScheme.outline.withValues(alpha: 0.3),
                                  colorScheme.outlineVariant.withValues(
                                    alpha: 0.3,
                                  ),
                                ],
                              ),
                        boxShadow: isActive
                            ? [
                                BoxShadow(
                                  color: const Color(
                                    0xFF25D366,
                                  ).withValues(alpha: 0.3),
                                  blurRadius: 12.0,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 1.0,
                                ),
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 6.0,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                            : [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.08),
                                  blurRadius: 4.0,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(buttonSize / 2),
                          onTap: isActive ? _onSendPressed : null,
                          child: Center(
                            child: widget.isLoading
                                ? SizedBox(
                                    width: isMobile ? 20.0 : 24.0,
                                    height: isMobile ? 20.0 : 24.0,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2.0,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white.withValues(alpha: 0.9),
                                      ),
                                    ),
                                  )
                                : AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 200),
                                    child: Icon(
                                      isActive
                                          ? Icons.send_rounded
                                          : Icons.send_outlined,
                                      key: ValueKey(isActive),
                                      color: isActive
                                          ? Colors.white
                                          : colorScheme.onSurfaceVariant
                                                .withValues(alpha: 0.5),
                                      size: isMobile ? 22.0 : 26.0,
                                    ),
                                  ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
