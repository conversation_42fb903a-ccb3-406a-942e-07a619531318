# Toxic Chat

A Flutter chat application that lets you chat with a dramatic "toxic ex" AI personality powered by OpenAI's GPT-3.5.

## Features

- 💬 Real-time chat with AI that plays a dramatic "toxic ex" character
- 🎭 Entertaining, theatrical responses with emojis and dramatic flair
- 📱 Responsive design that works on mobile, tablet, and desktop
- ✨ Smooth animations and message bubbles
- 🔄 Typing indicators and loading states
- 🎨 Modern Material Design 3 UI with gradient themes

## Architecture

- **Direct OpenAI Integration**: No separate backend needed - Flutter app communicates directly with OpenAI API
- **Clean Architecture**: Separation of concerns with services, models, and widgets
- **Responsive Design**: Adaptive layouts for different screen sizes
- **Error Handling**: Comprehensive error handling with user-friendly messages

## Setup

1. **Get OpenAI API Key**: Visit [OpenAI API Keys](https://platform.openai.com/api-keys) and create a new API key

2. **Configure API Key**: 
   - Open `lib/config/app_config.dart`
   - Replace `'your_openai_api_key_here'` with your actual API key

3. **Install Dependencies**:
   ```bash
   flutter pub get
   ```

4. **Run the App**:
   ```bash
   flutter run
   ```

## Security Note

⚠️ **Important**: This example stores the API key in source code for simplicity. For production apps, use:
- Environment variables with `flutter_dotenv`
- Secure storage packages like `flutter_secure_storage`
- A backend proxy to handle OpenAI requests

## Project Structure

```
lib/
├── config/
│   └── app_config.dart          # API key configuration
├── models/
│   └── message.dart             # Message data model
├── screens/
│   └── chat_screen.dart         # Main chat interface
├── services/
│   ├── chat_service.dart        # Chat service wrapper
│   └── openai_service.dart      # Direct OpenAI API integration
├── widgets/
│   ├── message_bubble.dart      # Chat message bubbles
│   ├── message_input.dart       # Message input field
│   └── typing_indicator.dart    # Typing animation
├── utils/
│   └── responsive_utils.dart    # Responsive design utilities
└── main.dart                    # App entry point
```

## How It Works

1. User types a message in the chat interface
2. Flutter app sends the message directly to OpenAI's API
3. OpenAI responds with a "toxic ex" personality message
4. The response is displayed with smooth animations
5. Chat history is maintained for context in conversations

## Dependencies

- `flutter`: The Flutter SDK
- `http`: For making HTTP requests to OpenAI API
- `cupertino_icons`: iOS-style icons

## Testing

Run the test suite:
```bash
flutter test
```

## Contributing

Feel free to submit issues and pull requests to improve the app!

## License

This project is for educational purposes.
