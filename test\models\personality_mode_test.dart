import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/personality_mode.dart';

void main() {
  group('PersonalityMode', () {
    test('should have all required personality modes', () {
      expect(PersonalityMode.values.length, equals(5));
      expect(PersonalityMode.values, contains(PersonalityMode.toxic));
      expect(PersonalityMode.values, contains(PersonalityMode.witch));
      expect(PersonalityMode.values, contains(PersonalityMode.jealous));
      expect(PersonalityMode.values, contains(PersonalityMode.dramatic));
      expect(PersonalityMode.values, contains(PersonalityMode.sarcastic));
    });

    test('should return correct display names', () {
      expect(PersonalityMode.toxic.displayName, equals('Toxic'));
      expect(PersonalityMode.witch.displayName, equals('Witch'));
      expect(PersonalityMode.jealous.displayName, equals('Jealous'));
      expect(PersonalityMode.dramatic.displayName, equals('Dramatic'));
      expect(PersonalityMode.sarcastic.displayName, equals('Sarcastic'));
    });

    test('should return correct descriptions', () {
      expect(PersonalityMode.toxic.description, contains('Confrontational'));
      expect(PersonalityMode.witch.description, contains('Mystical'));
      expect(PersonalityMode.jealous.description, contains('Possessive'));
      expect(PersonalityMode.dramatic.description, contains('Over-the-top'));
      expect(PersonalityMode.sarcastic.description, contains('Witty'));
    });

    test('should convert from string correctly', () {
      expect(PersonalityModeExtension.fromString('toxic'), equals(PersonalityMode.toxic));
      expect(PersonalityModeExtension.fromString('WITCH'), equals(PersonalityMode.witch));
      expect(PersonalityModeExtension.fromString('Jealous'), equals(PersonalityMode.jealous));
      expect(PersonalityModeExtension.fromString('invalid'), equals(PersonalityMode.toxic));
    });

    test('should return correct string values', () {
      expect(PersonalityMode.toxic.value, equals('toxic'));
      expect(PersonalityMode.witch.value, equals('witch'));
      expect(PersonalityMode.jealous.value, equals('jealous'));
      expect(PersonalityMode.dramatic.value, equals('dramatic'));
      expect(PersonalityMode.sarcastic.value, equals('sarcastic'));
    });
  });
}