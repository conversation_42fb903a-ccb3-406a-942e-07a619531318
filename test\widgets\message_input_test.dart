import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/widgets/message_input.dart';

void main() {
  group('MessageInput Widget Tests', () {
    testWidgets('should render with initial state', (WidgetTester tester) async {
      bool messageSent = false;
      String sentMessage = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {
                messageSent = true;
                sentMessage = message;
              },
            ),
          ),
        ),
      );

      // Verify initial state
      expect(find.byType(TextField), findsOneWidget);
      expect(find.byIcon(Icons.send_outlined), findsOneWidget);
      expect(find.text('Type a message...'), findsOneWidget);
      expect(messageSent, false);
    });

    testWidgets('should enable send button when text is entered', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {},
            ),
          ),
        ),
      );

      // Initially send button should be disabled (grey)
      expect(find.byIcon(Icons.send_outlined), findsOneWidget);

      // Enter text
      await tester.enterText(find.byType(TextField), 'Hello world');
      await tester.pump();

      // Send button should now be enabled (colored) and icon should change
      expect(find.byIcon(Icons.send_rounded), findsOneWidget);
      expect(find.text('Hello world'), findsOneWidget);
    });

    testWidgets('should call onSendMessage when send button is pressed', (WidgetTester tester) async {
      bool messageSent = false;
      String sentMessage = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {
                messageSent = true;
                sentMessage = message;
              },
            ),
          ),
        ),
      );

      // Enter text
      await tester.enterText(find.byType(TextField), 'Test message');
      await tester.pump();

      // Tap send button
      await tester.tap(find.byIcon(Icons.send_rounded));
      await tester.pump();

      // Verify message was sent
      expect(messageSent, true);
      expect(sentMessage, 'Test message');
    });

    testWidgets('should clear input field after sending message', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {},
            ),
          ),
        ),
      );

      // Enter text
      await tester.enterText(find.byType(TextField), 'Test message');
      await tester.pump();

      // Verify text is present
      expect(find.text('Test message'), findsOneWidget);

      // Tap send button
      await tester.tap(find.byIcon(Icons.send_rounded));
      await tester.pump();

      // Verify input field is cleared
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, isEmpty);
    });

    testWidgets('should handle submit via keyboard', (WidgetTester tester) async {
      bool messageSent = false;
      String sentMessage = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {
                messageSent = true;
                sentMessage = message;
              },
            ),
          ),
        ),
      );

      // Enter text
      await tester.enterText(find.byType(TextField), 'Keyboard submit');
      await tester.pump();

      // Submit via keyboard
      await tester.testTextInput.receiveAction(TextInputAction.send);
      await tester.pump();

      // Verify message was sent
      expect(messageSent, true);
      expect(sentMessage, 'Keyboard submit');
    });

    testWidgets('should not send empty messages', (WidgetTester tester) async {
      bool messageSent = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {
                messageSent = true;
              },
            ),
          ),
        ),
      );

      // Try to send empty message (should be inactive with outlined icon)
      await tester.tap(find.byIcon(Icons.send_outlined));
      await tester.pump();

      // Verify no message was sent
      expect(messageSent, false);
    });

    testWidgets('should not send whitespace-only messages', (WidgetTester tester) async {
      bool messageSent = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {
                messageSent = true;
              },
            ),
          ),
        ),
      );

      // Enter whitespace only
      await tester.enterText(find.byType(TextField), '   ');
      await tester.pump();

      // Try to send (should be inactive with outlined icon)
      await tester.tap(find.byIcon(Icons.send_outlined));
      await tester.pump();

      // Verify no message was sent
      expect(messageSent, false);
    });

    testWidgets('should show loading state when isLoading is true', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {},
              isLoading: true,
            ),
          ),
        ),
      );

      // Verify loading state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Sending...'), findsOneWidget);
    });

    testWidgets('should disable input when loading', (WidgetTester tester) async {
      bool messageSent = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {
                messageSent = true;
              },
              isLoading: true,
            ),
          ),
        ),
      );

      // Try to enter text (should be disabled)
      await tester.enterText(find.byType(TextField), 'Test');
      await tester.pump();

      // Try to send (should not work)
      await tester.tap(find.byType(CircularProgressIndicator));
      await tester.pump();

      // Verify no message was sent
      expect(messageSent, false);
    });

    testWidgets('should enforce message length limit', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {},
            ),
          ),
        ),
      );

      // Enter very long text (over 1000 characters)
      final longText = 'a' * 1001;
      await tester.enterText(find.byType(TextField), longText);
      await tester.pump();

      // The TextField should enforce maxLength of 1000
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.maxLength, 1000);
    });

    testWidgets('should trim whitespace from messages', (WidgetTester tester) async {
      String sentMessage = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {
                sentMessage = message;
              },
            ),
          ),
        ),
      );

      // Enter text with leading/trailing whitespace
      await tester.enterText(find.byType(TextField), '  Hello world  ');
      await tester.pump();

      // Send message (should be active with rounded icon)
      await tester.tap(find.byIcon(Icons.send_rounded));
      await tester.pump();

      // Verify whitespace was trimmed
      expect(sentMessage, 'Hello world');
    });

    testWidgets('should have proper accessibility labels', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(
              onSendMessage: (message) {},
            ),
          ),
        ),
      );

      // Verify text field is accessible
      expect(find.byType(TextField), findsOneWidget);
      expect(find.byIcon(Icons.send_outlined), findsOneWidget);
    });
  });
}