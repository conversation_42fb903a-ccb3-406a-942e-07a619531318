import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:toxic_chat/providers/theme_provider.dart';
import 'package:toxic_chat/screens/chat_screen.dart';
import 'package:toxic_chat/widgets/message_bubble.dart';
import 'package:toxic_chat/widgets/message_input.dart';
import 'package:toxic_chat/widgets/typing_indicator.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/utils/responsive_utils.dart';

void main() {
  group('Responsive Design Tests', () {
    testWidgets('ChatScreen adapts to mobile screen size', (
      WidgetTester tester,
    ) async {
      // Set mobile screen size
      await tester.binding.setSurfaceSize(
        const Size(375, 667),
      ); // iPhone SE size

      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const MaterialApp(home: ChatScreen()),
        ),
      );

      // Verify the screen renders without overflow
      expect(find.byType(ChatScreen), findsOneWidget);
      expect(find.byType(MessageInput), findsOneWidget);

      // Check that no overflow errors occur
      expect(tester.takeException(), isNull);
    });

    testWidgets('ChatScreen adapts to tablet screen size', (
      WidgetTester tester,
    ) async {
      // Set tablet screen size
      await tester.binding.setSurfaceSize(const Size(768, 1024)); // iPad size

      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const MaterialApp(home: ChatScreen()),
        ),
      );

      // Verify the screen renders without overflow
      expect(find.byType(ChatScreen), findsOneWidget);
      expect(find.byType(MessageInput), findsOneWidget);

      // Check that no overflow errors occur
      expect(tester.takeException(), isNull);
    });

    testWidgets('ChatScreen adapts to desktop screen size', (
      WidgetTester tester,
    ) async {
      // Set desktop screen size
      await tester.binding.setSurfaceSize(
        const Size(1200, 800),
      ); // Desktop size

      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const MaterialApp(home: ChatScreen()),
        ),
      );

      // Verify the screen renders without overflow
      expect(find.byType(ChatScreen), findsOneWidget);
      expect(find.byType(MessageInput), findsOneWidget);

      // Check that no overflow errors occur
      expect(tester.takeException(), isNull);
    });

    testWidgets('MessageBubble has responsive constraints', (
      WidgetTester tester,
    ) async {
      final message = Message(
        text: 'Test message for responsive design',
        isUser: true,
        timestamp: DateTime.now(),
        role: 'user',
      );

      // Test mobile size
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: MessageBubble(message: message)),
        ),
      );

      expect(find.byType(MessageBubble), findsOneWidget);
      expect(tester.takeException(), isNull);

      // Test tablet size
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: MessageBubble(message: message)),
        ),
      );

      expect(find.byType(MessageBubble), findsOneWidget);
      expect(tester.takeException(), isNull);

      // Test desktop size
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: MessageBubble(message: message)),
        ),
      );

      expect(find.byType(MessageBubble), findsOneWidget);
      expect(tester.takeException(), isNull);
    });

    testWidgets('MessageInput adapts to different screen sizes', (
      WidgetTester tester,
    ) async {
      // Test mobile size
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(onSendMessage: (text) {}, isLoading: false),
          ),
        ),
      );

      expect(find.byType(MessageInput), findsOneWidget);
      expect(find.byType(TextField), findsOneWidget);
      expect(tester.takeException(), isNull);

      // Test desktop size
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageInput(onSendMessage: (text) {}, isLoading: false),
          ),
        ),
      );

      expect(find.byType(MessageInput), findsOneWidget);
      expect(find.byType(TextField), findsOneWidget);
      expect(tester.takeException(), isNull);
    });

    testWidgets('TypingIndicator adapts to different screen sizes', (
      WidgetTester tester,
    ) async {
      // Test mobile size
      await tester.binding.setSurfaceSize(const Size(375, 667));
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: TypingIndicator(isVisible: true)),
        ),
      );

      expect(find.byType(TypingIndicator), findsOneWidget);
      expect(tester.takeException(), isNull);

      // Test desktop size
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: TypingIndicator(isVisible: true)),
        ),
      );

      expect(find.byType(TypingIndicator), findsOneWidget);
      expect(tester.takeException(), isNull);
    });

    group('ResponsiveUtils Tests', () {
      testWidgets('getScreenType returns correct screen types', (
        WidgetTester tester,
      ) async {
        // Test mobile
        await tester.binding.setSurfaceSize(const Size(375, 667));
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                expect(
                  ResponsiveUtils.getScreenType(context),
                  ScreenType.mobile,
                );
                expect(ResponsiveUtils.isMobile(context), isTrue);
                expect(ResponsiveUtils.isTablet(context), isFalse);
                expect(ResponsiveUtils.isDesktop(context), isFalse);
                return Container();
              },
            ),
          ),
        );

        // Test tablet
        await tester.binding.setSurfaceSize(const Size(768, 1024));
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                expect(
                  ResponsiveUtils.getScreenType(context),
                  ScreenType.tablet,
                );
                expect(ResponsiveUtils.isMobile(context), isFalse);
                expect(ResponsiveUtils.isTablet(context), isTrue);
                expect(ResponsiveUtils.isDesktop(context), isFalse);
                return Container();
              },
            ),
          ),
        );

        // Test desktop
        await tester.binding.setSurfaceSize(const Size(1200, 800));
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                expect(
                  ResponsiveUtils.getScreenType(context),
                  ScreenType.desktop,
                );
                expect(ResponsiveUtils.isMobile(context), isFalse);
                expect(ResponsiveUtils.isTablet(context), isFalse);
                expect(ResponsiveUtils.isDesktop(context), isTrue);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('getResponsiveFontSize scales correctly', (
        WidgetTester tester,
      ) async {
        const baseFontSize = 16.0;

        // Test mobile
        await tester.binding.setSurfaceSize(const Size(375, 667));
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                final fontSize = ResponsiveUtils.getResponsiveFontSize(
                  context,
                  baseFontSize,
                );
                expect(
                  fontSize,
                  greaterThanOrEqualTo(baseFontSize * 0.9),
                ); // Allow some tolerance
                expect(fontSize, lessThanOrEqualTo(baseFontSize * 1.1));
                return Container();
              },
            ),
          ),
        );

        // Test tablet - should be larger than mobile
        await tester.binding.setSurfaceSize(const Size(768, 1024));
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                final fontSize = ResponsiveUtils.getResponsiveFontSize(
                  context,
                  baseFontSize,
                );
                expect(
                  fontSize,
                  greaterThan(baseFontSize),
                ); // Should be larger than base
                return Container();
              },
            ),
          ),
        );

        // Test desktop - should be largest
        await tester.binding.setSurfaceSize(const Size(1200, 800));
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                final fontSize = ResponsiveUtils.getResponsiveFontSize(
                  context,
                  baseFontSize,
                );
                expect(
                  fontSize,
                  greaterThan(baseFontSize * 1.1),
                ); // Should be larger than tablet
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('getMessageBubbleConstraints returns valid constraints', (
        WidgetTester tester,
      ) async {
        // Test mobile
        await tester.binding.setSurfaceSize(const Size(375, 667));
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                final constraints = ResponsiveUtils.getMessageBubbleConstraints(
                  context,
                );
                expect(constraints.maxWidth, greaterThan(200.0));
                expect(constraints.maxWidth, lessThanOrEqualTo(600.0));
                expect(constraints.minWidth, equals(100.0));
                return Container();
              },
            ),
          ),
        );

        // Test tablet
        await tester.binding.setSurfaceSize(const Size(768, 1024));
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                final constraints = ResponsiveUtils.getMessageBubbleConstraints(
                  context,
                );
                expect(constraints.maxWidth, greaterThan(200.0));
                expect(constraints.maxWidth, lessThanOrEqualTo(600.0));
                expect(constraints.minWidth, equals(100.0));
                return Container();
              },
            ),
          ),
        );

        // Test desktop
        await tester.binding.setSurfaceSize(const Size(1200, 800));
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (context) {
                final constraints = ResponsiveUtils.getMessageBubbleConstraints(
                  context,
                );
                expect(
                  constraints.maxWidth,
                  equals(600.0),
                ); // Should be clamped to max
                expect(constraints.minWidth, equals(100.0));
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('Layout Adaptation Tests', () {
      testWidgets('Chat layout centers content on desktop', (
        WidgetTester tester,
      ) async {
        await tester.binding.setSurfaceSize(const Size(1200, 800));

        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => ThemeProvider(),
            child: const MaterialApp(home: ChatScreen()),
          ),
        );

        // Verify that the chat content is centered on desktop
        expect(find.byType(ChatScreen), findsOneWidget);
        expect(
          find.byType(Center),
          findsWidgets,
        ); // Should have centering widgets
        expect(tester.takeException(), isNull);
      });

      testWidgets(
        'Message bubbles have appropriate margins on different screens',
        (WidgetTester tester) async {
          final message = Message(
            text: 'Test message',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          );

          // Test that message bubbles render correctly on all screen sizes
          for (final size in [
            const Size(375, 667), // Mobile
            const Size(768, 1024), // Tablet
            const Size(1200, 800), // Desktop
          ]) {
            await tester.binding.setSurfaceSize(size);
            await tester.pumpWidget(
              MaterialApp(
                home: Scaffold(body: MessageBubble(message: message)),
              ),
            );

            expect(find.byType(MessageBubble), findsOneWidget);
            expect(find.text('Test message'), findsOneWidget);
            expect(tester.takeException(), isNull);
          }
        },
      );
    });
  });
}
