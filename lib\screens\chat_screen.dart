import 'package:flutter/material.dart';
import 'package:toxic_chat/models/personality_mode.dart';
import 'package:toxic_chat/services/personality_service.dart';
import 'package:toxic_chat/widgets/message_bubble.dart';
import '../models/message.dart';
import '../services/chat_service.dart';
import '../services/language_detection_service.dart';
import '../widgets/message_input.dart';
import '../widgets/typing_indicator.dart';
import '../utils/responsive_utils.dart';
import 'settings_screen.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  final List<Message> _messages = [];
  final ChatService _chatService = ChatService();
  final LanguageDetectionService _languageService = LanguageDetectionService();
  final PersonalityService _personalityService = PersonalityService();
  final ScrollController _scrollController = ScrollController();
  final Map<int, AnimationController> _animationControllers = {};

  bool _isLoading = false;
  bool _isServiceAvailable = true;
  bool _isShowingAiTyping = false;
  bool _autoScrollEnabled = true;
  final bool _showTimestamps = true;

  String _conversationLanguage = 'en';
  PersonalityMode _currentPersonality = PersonalityMode.toxic;

  @override
  void initState() {
    super.initState();
    _checkServiceAvailability();
    _loadPersonality();
  }

  Future<void> _loadPersonality() async {
    await _personalityService.loadConfiguration();
    setState(() {
      _currentPersonality =
          _personalityService.currentConfig.currentPersonality;
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _chatService.dispose();
    for (final controller in _animationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _checkServiceAvailability() async {
    final wasAvailable = _isServiceAvailable;
    final isAvailable = await _chatService.isServiceAvailable();

    if (mounted) {
      setState(() {
        _isServiceAvailable = isAvailable;
      });

      if (!isAvailable) {
        _showError(
          'OpenAI service is not available. Please check your API key configuration.',
          showRetry: true,
          onRetry: _checkServiceAvailability,
        );
      } else if (!wasAvailable && isAvailable) {
        _showSuccess('OpenAI service is now available!');
      }
    }
  }

  Future<void> _sendMessage(String text) async {
    if (text.trim().isEmpty || _isLoading || _isShowingAiTyping) return;

    final detectedLanguage = await _languageService.detectLanguage(text.trim());

    setState(() {
      if (_messages.isEmpty || detectedLanguage != _conversationLanguage) {
        _conversationLanguage = detectedLanguage;
      }
    });

    final userMessage = Message(
      text: text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
      role: 'user',
      language: detectedLanguage,
    );

    _addMessage(userMessage);

    setState(() {
      _isLoading = true;
    });

    try {
      final aiResponse = await _chatService.sendMessage(
        _messages,
        language: _conversationLanguage,
        personality: _currentPersonality,
      );

      setState(() {
        _isLoading = false;
      });

      _addMessage(aiResponse);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      final errorMessage = _getErrorMessage(e);
      final isRetryable = _isErrorRetryable(e);

      _showError(
        errorMessage,
        showRetry: isRetryable,
        onRetry: isRetryable
            ? () {
                final lastUserMessage = _messages.last;
                _sendMessage(lastUserMessage.text);
              }
            : null,
      );
    }
  }

  void _addMessage(Message message) {
    if (!mounted) return;

    final controller = AnimationController(
      duration: Duration(milliseconds: message.isUser ? 300 : 500),
      vsync: this,
    );

    if (!message.isUser) {
      setState(() {
        _isShowingAiTyping = true;
      });

      Future.delayed(const Duration(milliseconds: 1200), () {
        if (mounted) {
          setState(() {
            _messages.add(message);
            _isShowingAiTyping = false;
          });

          _animationControllers[_messages.length - 1] = controller;
          controller.forward();
        }
      });
    } else {
      setState(() {
        _messages.add(message);
      });

      _animationControllers[_messages.length - 1] = controller;
      controller.forward();
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final delay = message.isUser ? 100 : 1300;
      Future.delayed(Duration(milliseconds: delay), () {
        _scrollToBottom(force: true);
      });
    });
  }

  void _scrollToBottom({bool force = false}) {
    if (_scrollController.hasClients && (_autoScrollEnabled || force)) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeOutCubic,
      );
    }
  }

  void _showError(
    String message, {
    bool showRetry = true,
    VoidCallback? onRetry,
  }) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).clearSnackBars();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 24.0),
            const SizedBox(width: 16.0),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  fontSize: 16.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.redAccent.shade700,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        duration: const Duration(seconds: 8),
        action: showRetry
            ? SnackBarAction(
                label: 'RETRY',
                textColor: Colors.white,
                backgroundColor: Colors.redAccent.shade400,
                onPressed: onRetry ?? _getDefaultRetryAction(),
              )
            : null,
      ),
    );
  }

  VoidCallback _getDefaultRetryAction() {
    return () {
      if (_messages.isNotEmpty && _messages.last.isUser) {
        final lastUserMessage = _messages.last;
        _sendMessage(lastUserMessage.text);
      } else {
        _checkServiceAvailability();
      }
    };
  }

  void _showSuccess(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.check_circle_outline,
              color: Colors.white,
              size: 24.0,
            ),
            const SizedBox(width: 16.0),
            Text(
              message,
              style: const TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  String _getErrorMessage(dynamic error) {
    if (error is ChatServiceException) {
      switch (error.errorType) {
        case ChatServiceErrorType.network:
          return 'Network issue. Please check your connection.';
        case ChatServiceErrorType.timeout:
          return 'Request timed out. The server might be busy.';
        case ChatServiceErrorType.authentication:
          return 'Authentication failed. Check your API key.';
        case ChatServiceErrorType.rateLimit:
          return 'Too many requests. Please wait a moment.';
        case ChatServiceErrorType.serverError:
          return 'Server error. Please try again later.';
        case ChatServiceErrorType.validation:
          return 'Invalid request sent. Please try again.';
        case ChatServiceErrorType.unknown:
          return error.message.isNotEmpty
              ? error.message
              : 'An unexpected error occurred.';
      }
    }
    return 'An unexpected error occurred.';
  }

  bool _isErrorRetryable(dynamic error) {
    if (error is ChatServiceException) {
      return error.isRetryable;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final maxContentWidth = ResponsiveUtils.getMaxContentWidth(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Toxic Chat',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () {
              Navigator.of(context)
                  .push(
                    MaterialPageRoute(
                      builder: (context) => const SettingsScreen(),
                    ),
                  )
                  .then((_) => _loadPersonality());
            },
          ),
        ],
        backgroundColor: theme.scaffoldBackgroundColor,
        elevation: 0,
      ),
      body: Stack(
        children: [
          Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: isDesktop ? maxContentWidth : double.infinity,
              ),
              child: Column(
                children: [
                  Expanded(
                    child: _messages.isEmpty && !_isShowingAiTyping
                        ? _buildEmptyState(theme)
                        : _buildMessagesList(),
                  ),
                  _buildMessageInputContainer(),
                ],
              ),
            ),
          ),
          if (_buildScrollToBottomButton(context, isMobile) != null)
            _buildScrollToBottomButton(context, isMobile)!,
        ],
      ),
    );
  }

  Widget _buildMessagesList() {
    return NotificationListener<ScrollNotification>(
      onNotification: (scrollInfo) {
        if (scrollInfo is ScrollUpdateNotification) {
          final scrollPosition = _scrollController.position;
          final isNearBottom =
              scrollPosition.maxScrollExtent - scrollPosition.pixels < 120;

          if (isNearBottom != _autoScrollEnabled) {
            setState(() {
              _autoScrollEnabled = isNearBottom;
            });
          }
        }
        return false;
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        itemCount: _messages.length + (_isShowingAiTyping ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _messages.length && _isShowingAiTyping) {
            return TypingIndicator(isVisible: _isShowingAiTyping);
          }

          final message = _messages[index];
          final animation = _animationControllers[index];

          return MessageBubble(
            message: message,
            animation: animation,
            showTimestamp: _showTimestamps,
            personality: _currentPersonality,
          );
        },
      ),
    );
  }

  Widget _buildMessageInputContainer() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: MessageInput(onSendMessage: _sendMessage, isLoading: _isLoading),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline_rounded,
            size: 100,
            color: theme.colorScheme.primary.withOpacity(0.7),
          ),
          const SizedBox(height: 24),
          Text(
            'Welcome to Toxic Chat',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ask me anything or just say hi!',
            style: theme.textTheme.titleMedium?.copyWith(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget? _buildScrollToBottomButton(BuildContext context, bool isMobile) {
    if (_autoScrollEnabled || _messages.isEmpty) {
      return null;
    }

    final colorScheme = Theme.of(context).colorScheme;
    final buttonSize = isMobile ? 48.0 : 56.0;

    return Positioned(
      bottom: isMobile ? 80.0 : 90.0,
      right: isMobile ? 16.0 : 24.0,
      child: AnimatedOpacity(
        opacity: _autoScrollEnabled ? 0.0 : 1.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          width: buttonSize,
          height: buttonSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: _getScrollButtonGradient(colorScheme),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8.0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(buttonSize / 2),
              onTap: () {
                _scrollToBottom(force: true);
                setState(() {
                  _autoScrollEnabled = true;
                });
              },
              child: Center(
                child: Icon(
                  Icons.keyboard_arrow_down_rounded,
                  color: Colors.white,
                  size: isMobile ? 24.0 : 28.0,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  LinearGradient _getScrollButtonGradient(ColorScheme colorScheme) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [colorScheme.primary, colorScheme.secondary],
    );
  }
}
