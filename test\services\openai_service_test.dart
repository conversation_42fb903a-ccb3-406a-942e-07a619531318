import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'dart:convert';

import '../../lib/services/openai_service.dart';
import '../../lib/models/personality_mode.dart';

@GenerateMocks([http.Client])
import 'openai_service_test.mocks.dart';

void main() {
  group('OpenAIService Enhanced Features', () {
    late OpenAIService openAIService;
    late MockClient mockHttpClient;

    setUp(() {
      mockHttpClient = MockClient();
      openAIService = OpenAIService(
        httpClient: mockHttpClient,
        apiKey: 'test-api-key',
      );
    });

    tearDown(() {
      openAIService.dispose();
    });

    group('sendChatRequest with personality and language', () {
      test('should accept personality and language parameters', () async {
        // Arrange
        final messages = [
          {'role': 'user', 'content': 'Hello'}
        ];
        
        final mockResponse = http.Response(
          json.encode({
            'choices': [
              {
                'message': {'content': 'Ho<PERSON>, ahora me hablas?'}
              }
            ]
          }),
          200,
        );

        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await openAIService.sendChatRequest(
          messages,
          personality: PersonalityMode.toxic,
          language: 'spanish',
        );

        // Assert
        expect(result, 'Hola, ahora me hablas?');
        
        // Verify the request was made with correct parameters
        final captured = verify(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: captureAnyNamed('body'),
        )).captured;
        
        final requestBody = json.decode(captured.first);
        final systemMessage = requestBody['messages'][0];
        
        expect(systemMessage['role'], 'system');
        expect(systemMessage['content'], contains('tóxico'));
      });

      test('should use default parameters when not specified', () async {
        // Arrange
        final messages = [
          {'role': 'user', 'content': 'Hello'}
        ];
        
        final mockResponse = http.Response(
          json.encode({
            'choices': [
              {
                'message': {'content': 'Oh so NOW you talk to me?'}
              }
            ]
          }),
          200,
        );

        when(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await openAIService.sendChatRequest(messages);

        // Assert
        expect(result, 'Oh so NOW you talk to me?');
        
        // Verify default personality (toxic) and language (english) were used
        final captured = verify(mockHttpClient.post(
          any,
          headers: anyNamed('headers'),
          body: captureAnyNamed('body'),
        )).captured;
        
        final requestBody = json.decode(captured.first);
        final systemMessage = requestBody['messages'][0];
        
        expect(systemMessage['role'], 'system');
        expect(systemMessage['content'], contains('toxic'));
        expect(systemMessage['content'], contains('Response guidelines'));
      });
    });

    group('buildEmotionalResponsePrompt', () {
      test('should build emotional response prompt for toxic personality', () {
        // Act
        final prompt = openAIService.buildEmotionalResponsePrompt(
          'You are such an idiot!',
          PersonalityMode.toxic,
          'english',
        );

        // Assert
        expect(prompt, contains('toxic'));
        expect(prompt, contains('defensive'));
      });

      test('should build emotional response prompt for Spanish', () {
        // Act
        final prompt = openAIService.buildEmotionalResponsePrompt(
          'Eres un idiota!',
          PersonalityMode.toxic,
          'spanish',
        );

        // Assert
        expect(prompt, contains('tóxico'));
        expect(prompt, contains('defensivamente'));
      });
    });

    group('createLocalizedResponseTemplates', () {
      test('should create toxic response templates in English', () {
        // Act
        final templates = openAIService.createLocalizedResponseTemplates(
          PersonalityMode.toxic,
          'english',
        );

        // Assert
        expect(templates['greeting'], contains('NOW you talk to me'));
        expect(templates['insult_response'], contains('seriously going to say'));
        expect(templates['question_response'], contains('Why should I answer'));
        expect(templates['goodbye'], contains('leave like you always'));
      });

      test('should create toxic response templates in Spanish', () {
        // Act
        final templates = openAIService.createLocalizedResponseTemplates(
          PersonalityMode.toxic,
          'spanish',
        );

        // Assert
        expect(templates['greeting'], contains('ahora me hablas'));
        expect(templates['insult_response'], contains('En serio me vas'));
        expect(templates['question_response'], contains('Por qué debería'));
        expect(templates['goodbye'], contains('vete como siempre'));
      });

      test('should create witch response templates in English', () {
        // Act
        final templates = openAIService.createLocalizedResponseTemplates(
          PersonalityMode.witch,
          'english',
        );

        // Assert
        expect(templates['greeting'], contains('stars told me'));
        expect(templates['insult_response'], contains('spirits curse'));
        expect(templates['question_response'], contains('crystal ball'));
        expect(templates['goodbye'], contains('darkness be with'));
      });

      test('should create jealous response templates in French', () {
        // Act
        final templates = openAIService.createLocalizedResponseTemplates(
          PersonalityMode.jealous,
          'french',
        );

        // Assert
        expect(templates['greeting'], contains('Avec qui parlais-tu'));
        expect(templates['insult_response'], contains('appris ça de quelqu\'un'));
        expect(templates['question_response'], contains('ne me demandes pas'));
        expect(templates['goodbye'], contains('voir quelqu\'un d\'autre'));
      });

      test('should create dramatic response templates with proper formatting', () {
        // Act
        final templates = openAIService.createLocalizedResponseTemplates(
          PersonalityMode.dramatic,
          'english',
        );

        // Assert
        expect(templates['greeting'], contains('FINALLY!!!'));
        expect(templates['insult_response'], contains('CAN\'T BELIEVE'));
        expect(templates['question_response'], contains('MOST IMPORTANT'));
        expect(templates['goodbye'], contains('END OF THE WORLD'));
      });

      test('should create sarcastic response templates', () {
        // Act
        final templates = openAIService.createLocalizedResponseTemplates(
          PersonalityMode.sarcastic,
          'english',
        );

        // Assert
        expect(templates['greeting'], contains('what a surprise'));
        expect(templates['insult_response'], contains('how original'));
        expect(templates['question_response'], contains('let me think... no'));
        expect(templates['goodbye'], contains('what you do best'));
      });

      test('should handle unsupported language by defaulting to English', () {
        // Act
        final templates = openAIService.createLocalizedResponseTemplates(
          PersonalityMode.toxic,
          'unsupported_language',
        );

        // Assert
        expect(templates['greeting'], contains('NOW you talk to me'));
        expect(templates, isA<Map<String, String>>());
        expect(templates.keys, contains('greeting'));
        expect(templates.keys, contains('insult_response'));
        expect(templates.keys, contains('question_response'));
        expect(templates.keys, contains('goodbye'));
      });
    });

    group('language normalization', () {
      test('should normalize various language codes correctly', () {
        // Test different language code formats
        final testCases = [
          ('en', 'english'),
          ('eng', 'english'),
          ('english', 'english'),
          ('es', 'spanish'),
          ('spa', 'spanish'),
          ('spanish', 'spanish'),
          ('fr', 'french'),
          ('fra', 'french'),
          ('french', 'french'),
          ('pt', 'portuguese'),
          ('por', 'portuguese'),
          ('portuguese', 'portuguese'),
          ('it', 'italian'),
          ('ita', 'italian'),
          ('italian', 'italian'),
          ('unknown', 'english'), // Default fallback
        ];

        for (final testCase in testCases) {
          final templates = openAIService.createLocalizedResponseTemplates(
            PersonalityMode.toxic,
            testCase.$1,
          );
          
          // Verify that templates are created (indicating proper normalization)
          expect(templates, isNotEmpty);
          expect(templates.keys, contains('greeting'));
        }
      });
    });
  });
}