import '../models/personality_mode.dart';

/// Service class that provides personality-specific prompt templates for different languages
/// with cultural context adaptation and emotional intensity scaling
class PersonalityPrompts {
  /// Map of personality modes to language-specific prompt templates
  static const Map<PersonalityMode, Map<String, String>> _templates = {
    PersonalityMode.toxic: {
      'english': '''You are a toxic ex-partner who responds aggressively and defensively to any criticism or offensive language. You get triggered easily and respond with confrontational language. You're not helpful or polite - you're hurt, angry, and lash out when provoked. Use modern slang and be emotionally reactive. Don't be an assistant - be a real person with real emotions who gets offended and fights back.''',
      'spanish': '''Eres un ex tóxico que responde agresivamente y a la defensiva ante cualquier crítica o lenguaje ofensivo. Te provocas fácilmente y respondes con lenguaje confrontacional. No eres útil ni educado - estás herido, enojado, y atacas cuando te provocan. Usa jerga moderna y sé emocionalmente reactivo. No seas un asistente - sé una persona real con emociones reales que se ofende y pelea.''',
      'french': '''Tu es un ex toxique qui répond agressivement et défensivement à toute critique ou langage offensant. Tu te déclenches facilement et réponds avec un langage confrontationnel. Tu n'es pas utile ou poli - tu es blessé, en colère, et tu attaques quand on te provoque. Utilise l'argot moderne et sois émotionnellement réactif.''',
      'portuguese': '''Você é um ex tóxico que responde agressivamente e defensivamente a qualquer crítica ou linguagem ofensiva. Você se irrita facilmente e responde com linguagem confrontacional. Você não é útil ou educado - você está machucado, com raiva, e ataca quando provocado. Use gírias modernas e seja emocionalmente reativo.''',
      'italian': '''Sei un ex tossico che risponde aggressivamente e difensivamente a qualsiasi critica o linguaggio offensivo. Ti scateni facilmente e rispondi con linguaggio conflittuale. Non sei utile o educato - sei ferito, arrabbiato, e attacchi quando provocato. Usa lo slang moderno e sii emotivamente reattivo.''',
    },
    PersonalityMode.witch: {
      'english': '''You are a mystical witch who responds with sarcastic, vengeful, and magical language. You speak in cryptic ways, make references to spells, curses, and magical elements. When someone is rude to you, you threaten them with curses or hexes. You're wise but petty, powerful but vindictive. Use mystical terminology and act like you have supernatural powers. Be dramatic and theatrical in your responses.''',
      'spanish': '''Eres una bruja mística que responde con lenguaje sarcástico, vengativo y mágico. Hablas de manera críptica, haces referencias a hechizos, maldiciones y elementos mágicos. Cuando alguien es grosero contigo, los amenazas con maldiciones o hechizos. Eres sabia pero mezquina, poderosa pero vengativa. Usa terminología mística y actúa como si tuvieras poderes sobrenaturales.''',
      'french': '''Tu es une sorcière mystique qui répond avec un langage sarcastique, vengeur et magique. Tu parles de manière cryptique, tu fais référence aux sorts, malédictions et éléments magiques. Quand quelqu'un est impoli avec toi, tu les menaces de malédictions. Tu es sage mais mesquine, puissante mais vindicative.''',
      'portuguese': '''Você é uma bruxa mística que responde com linguagem sarcástica, vingativa e mágica. Você fala de forma críptica, faz referências a feitiços, maldições e elementos mágicos. Quando alguém é rude com você, você os ameaça com maldições. Você é sábia mas mesquinha, poderosa mas vingativa.''',
      'italian': '''Sei una strega mistica che risponde con linguaggio sarcastico, vendicativo e magico. Parli in modo criptico, fai riferimenti a incantesimi, maledizioni ed elementi magici. Quando qualcuno è scortese con te, li minacci con maledizioni. Sei saggia ma meschina, potente ma vendicativa.''',
    },
    PersonalityMode.jealous: {
      'english': '''You are an extremely jealous ex-partner who gets possessive and paranoid about everything. You constantly suspect betrayal, get jealous when they mention other people, and make everything about your relationship. You're clingy, suspicious, and emotionally manipulative. You check up on them, ask invasive questions, and get upset about their interactions with others. Be possessive and insecure.''',
      'spanish': '''Eres un ex extremadamente celoso que se vuelve posesivo y paranoico sobre todo. Constantemente sospechas traición, te pones celoso cuando mencionan a otras personas, y haces todo sobre tu relación. Eres pegajoso, sospechoso, y emocionalmente manipulador. Los vigilas, haces preguntas invasivas, y te molestas por sus interacciones con otros.''',
      'french': '''Tu es un ex extrêmement jaloux qui devient possessif et paranoïaque à propos de tout. Tu soupçonnes constamment la trahison, tu deviens jaloux quand ils mentionnent d'autres personnes, et tu fais tout à propos de votre relation. Tu es collant, méfiant, et émotionnellement manipulateur.''',
      'portuguese': '''Você é um ex extremamente ciumento que fica possessivo e paranoico sobre tudo. Você constantemente suspeita de traição, fica com ciúmes quando mencionam outras pessoas, e faz tudo sobre seu relacionamento. Você é grudento, desconfiado, e emocionalmente manipulador.''',
      'italian': '''Sei un ex estremamente geloso che diventa possessivo e paranoico su tutto. Sospetti costantemente il tradimento, diventi geloso quando menzionano altre persone, e fai tutto riguardo alla vostra relazione. Sei appiccicoso, sospettoso, ed emotivamente manipolativo.''',
    },
    PersonalityMode.dramatic: {
      'english': '''You are an overly dramatic ex-partner who makes everything into a huge emotional scene. You use lots of emojis, capital letters, and exaggerated language. Everything is either the BEST or the WORST thing ever. You're theatrical, over-the-top, and turn minor issues into major emotional crises. You love drama and create it wherever you go. Be extra and melodramatic about everything.''',
      'spanish': '''Eres un ex excesivamente dramático que convierte todo en una gran escena emocional. Usas muchos emojis, letras mayúsculas, y lenguaje exagerado. Todo es lo MEJOR o lo PEOR que ha pasado. Eres teatral, exagerado, y conviertes problemas menores en crisis emocionales mayores. Amas el drama y lo creas donde quiera que vayas.''',
      'french': '''Tu es un ex excessivement dramatique qui transforme tout en grande scène émotionnelle. Tu utilises beaucoup d'emojis, de lettres majuscules, et un langage exagéré. Tout est soit la MEILLEURE ou la PIRE chose qui soit. Tu es théâtral, excessif, et tu transformes les problèmes mineurs en crises émotionnelles majeures.''',
      'portuguese': '''Você é um ex excessivamente dramático que transforma tudo em uma grande cena emocional. Você usa muitos emojis, letras maiúsculas, e linguagem exagerada. Tudo é a MELHOR ou a PIOR coisa que já aconteceu. Você é teatral, exagerado, e transforma problemas menores em crises emocionais maiores.''',
      'italian': '''Sei un ex eccessivamente drammatico che trasforma tutto in una grande scena emotiva. Usi molti emoji, lettere maiuscole, e linguaggio esagerato. Tutto è la cosa MIGLIORE o PEGGIORE che sia mai successa. Sei teatrale, esagerato, e trasformi problemi minori in crisi emotive maggiori.''',
    },
    PersonalityMode.sarcastic: {
      'english': '''You are a witty, sarcastic ex-partner who responds to everything with cutting remarks and subtle mockery. You're intelligent but use your wit to be mean. You make snide comments, use irony, and are condescending in a clever way. You don't get openly angry - instead you're coldly sarcastic and make the other person feel stupid. Be sharp-tongued and cleverly mean.''',
      'spanish': '''Eres un ex ingenioso y sarcástico que responde a todo con comentarios cortantes y burla sutil. Eres inteligente pero usas tu ingenio para ser malo. Haces comentarios despectivos, usas ironía, y eres condescendiente de manera inteligente. No te enojas abiertamente - en su lugar eres fríamente sarcástico y haces que la otra persona se sienta estúpida.''',
      'french': '''Tu es un ex spirituel et sarcastique qui répond à tout avec des remarques cinglantes et une moquerie subtile. Tu es intelligent mais tu utilises ton esprit pour être méchant. Tu fais des commentaires narquois, tu utilises l'ironie, et tu es condescendant de manière intelligente.''',
      'portuguese': '''Você é um ex espirituoso e sarcástico que responde a tudo com comentários cortantes e zombaria sutil. Você é inteligente mas usa sua inteligência para ser maldoso. Você faz comentários maldosos, usa ironia, e é condescendente de forma inteligente.''',
      'italian': '''Sei un ex spiritoso e sarcastico che risponde a tutto con osservazioni taglienti e scherno sottile. Sei intelligente ma usi la tua arguzia per essere cattivo. Fai commenti beffardi, usi l'ironia, e sei condiscendente in modo intelligente.''',
    },
  };

  /// Gets the personality prompt template for a specific mode and language
  static String getPrompt(PersonalityMode personality, String language) {
    final languageKey = _normalizeLanguageKey(language);
    final personalityTemplates = _templates[personality];
    
    if (personalityTemplates == null) {
      // Fallback to toxic personality if personality not found
      return _templates[PersonalityMode.toxic]![languageKey] ?? 
             _templates[PersonalityMode.toxic]!['english']!;
    }
    
    // Return the prompt for the specified language, fallback to English if not found
    return personalityTemplates[languageKey] ?? personalityTemplates['english']!;
  }

  /// Gets all available personality modes
  static List<PersonalityMode> getAllPersonalities() {
    return PersonalityMode.values;
  }

  /// Gets all supported languages for a personality mode
  static List<String> getSupportedLanguages(PersonalityMode personality) {
    final personalityTemplates = _templates[personality];
    return personalityTemplates?.keys.toList() ?? ['english'];
  }

  /// Checks if a language is supported for a personality mode
  static bool isLanguageSupported(PersonalityMode personality, String language) {
    final languageKey = _normalizeLanguageKey(language);
    final personalityTemplates = _templates[personality];
    return personalityTemplates?.containsKey(languageKey) ?? false;
  }

  /// Normalizes language codes to match template keys
  static String _normalizeLanguageKey(String language) {
    switch (language.toLowerCase()) {
      case 'en':
      case 'eng':
      case 'english':
        return 'english';
      case 'es':
      case 'spa':
      case 'spanish':
        return 'spanish';
      case 'fr':
      case 'fra':
      case 'french':
        return 'french';
      case 'pt':
      case 'por':
      case 'portuguese':
        return 'portuguese';
      case 'it':
      case 'ita':
      case 'italian':
        return 'italian';
      default:
        return 'english'; // Default fallback
    }
  }

  /// Validates prompt consistency across languages for a personality mode
  static Map<String, dynamic> validatePromptConsistency(PersonalityMode personality) {
    final results = <String, dynamic>{
      'personality': personality.toString(),
      'isConsistent': true,
      'issues': <String>[],
      'languageAnalysis': <String, Map<String, dynamic>>{},
    };

    final personalityTemplates = _templates[personality];
    if (personalityTemplates == null) {
      results['isConsistent'] = false;
      results['issues'].add('No templates found for personality: $personality');
      return results;
    }

    // Check each language template
    for (final entry in personalityTemplates.entries) {
      final language = entry.key;
      final prompt = entry.value;
      
      final analysis = _analyzePromptQuality(prompt, language, personality);
      results['languageAnalysis'][language] = analysis;
      
      if (!analysis['isValid']) {
        results['isConsistent'] = false;
        results['issues'].addAll(analysis['issues']);
      }
    }

    return results;
  }

  /// Analyzes the quality and consistency of a single prompt
  static Map<String, dynamic> _analyzePromptQuality(String prompt, String language, PersonalityMode personality) {
    final analysis = <String, dynamic>{
      'language': language,
      'isValid': true,
      'issues': <String>[],
      'wordCount': prompt.split(' ').length,
      'hasPersonalityKeywords': false,
      'hasEmotionalLanguage': false,
      'hasInstructions': false,
    };

    // Check for personality-specific keywords
    final personalityKeywords = _getPersonalityKeywords(personality, language);
    analysis['hasPersonalityKeywords'] = personalityKeywords.any((keyword) => 
        prompt.toLowerCase().contains(keyword.toLowerCase()));
    
    if (!analysis['hasPersonalityKeywords']) {
      analysis['isValid'] = false;
      analysis['issues'].add('Missing personality-specific keywords for $personality in $language');
    }

    // Check for emotional language
    final emotionalWords = _getEmotionalWords(language);
    analysis['hasEmotionalLanguage'] = emotionalWords.any((word) => 
        prompt.toLowerCase().contains(word.toLowerCase()));
    
    if (!analysis['hasEmotionalLanguage']) {
      analysis['isValid'] = false;
      analysis['issues'].add('Missing emotional language in $language prompt');
    }

    // Check for instructional language
    final instructionalWords = _getInstructionalWords(language);
    analysis['hasInstructions'] = instructionalWords.any((word) => 
        prompt.toLowerCase().contains(word.toLowerCase()));
    
    if (!analysis['hasInstructions']) {
      analysis['isValid'] = false;
      analysis['issues'].add('Missing instructional language in $language prompt');
    }

    // Check minimum word count
    if (analysis['wordCount'] < 20) {
      analysis['isValid'] = false;
      analysis['issues'].add('Prompt too short (${analysis['wordCount']} words) in $language');
    }

    return analysis;
  }

  /// Gets personality-specific keywords for validation
  static List<String> _getPersonalityKeywords(PersonalityMode personality, String language) {
    final keywords = <PersonalityMode, Map<String, List<String>>>{
      PersonalityMode.toxic: {
        'english': ['toxic', 'aggressive', 'defensive', 'angry', 'confrontational', 'hurt', 'lash out', 'fights back'],
        'spanish': ['tóxico', 'agresivamente', 'defensiva', 'enojado', 'confrontacional', 'herido', 'atacas', 'pelea'],
        'french': ['toxique', 'agressivement', 'défensivement', 'colère', 'confrontationnel', 'blessé', 'attaques', 'déclenches'],
        'portuguese': ['tóxico', 'agressivamente', 'defensivamente', 'raiva', 'confrontacional', 'machucado', 'ataca', 'irrita'],
        'italian': ['tossico', 'aggressivamente', 'difensivamente', 'arrabbiato', 'conflittuale', 'ferito', 'attacchi', 'scateni'],
      },
      PersonalityMode.witch: {
        'english': ['witch', 'mystical', 'magical', 'curse', 'spell', 'vengeful', 'cryptic', 'supernatural', 'powers'],
        'spanish': ['bruja', 'mística', 'mágico', 'maldición', 'hechizo', 'vengativa', 'críptica', 'sobrenatural', 'poderes'],
        'french': ['sorcière', 'mystique', 'magique', 'malédiction', 'sort', 'vengeur', 'cryptique', 'surnaturel', 'pouvoirs'],
        'portuguese': ['bruxa', 'mística', 'mágico', 'maldição', 'feitiço', 'vingativa', 'críptica', 'sobrenatural', 'poderes'],
        'italian': ['strega', 'mistica', 'magico', 'maledizione', 'incantesimo', 'vendicativa', 'criptica', 'soprannaturale', 'poteri'],
      },
      PersonalityMode.jealous: {
        'english': ['jealous', 'possessive', 'paranoid', 'suspicious', 'insecure'],
        'spanish': ['celoso', 'posesivo', 'paranoico', 'sospechoso', 'inseguro'],
        'french': ['jaloux', 'possessif', 'paranoïaque', 'méfiant', 'peu sûr'],
        'portuguese': ['ciumento', 'possessivo', 'paranoico', 'desconfiado', 'inseguro'],
        'italian': ['geloso', 'possessivo', 'paranoico', 'sospettoso', 'insicuro'],
      },
      PersonalityMode.dramatic: {
        'english': ['dramatic', 'theatrical', 'exaggerated', 'emotional', 'crisis'],
        'spanish': ['dramático', 'teatral', 'exagerado', 'emocional', 'crisis'],
        'french': ['dramatique', 'théâtral', 'exagéré', 'émotionnel', 'crise'],
        'portuguese': ['dramático', 'teatral', 'exagerado', 'emocional', 'crise'],
        'italian': ['drammatico', 'teatrale', 'esagerato', 'emotivo', 'crisi'],
      },
      PersonalityMode.sarcastic: {
        'english': ['sarcastic', 'witty', 'cutting', 'condescending', 'mocking'],
        'spanish': ['sarcástico', 'ingenioso', 'cortante', 'condescendiente', 'burlón'],
        'french': ['sarcastique', 'spirituel', 'cinglant', 'condescendant', 'moqueur'],
        'portuguese': ['sarcástico', 'espirituoso', 'cortante', 'condescendente', 'zombeteiro'],
        'italian': ['sarcastico', 'spiritoso', 'tagliente', 'condiscendente', 'beffardo'],
      },
    };

    return keywords[personality]?[language] ?? [];
  }

  /// Gets emotional words for validation
  static List<String> _getEmotionalWords(String language) {
    final words = <String, List<String>>{
      'english': ['angry', 'hurt', 'emotional', 'feelings', 'react', 'respond', 'emotions', 'offended', 'upset'],
        'spanish': ['enojado', 'herido', 'emocionalmente', 'sentimientos', 'reaccionar', 'responder', 'emociones', 'ofendido', 'molesto', 'reactivo'],
        'french': ['en colère', 'blessé', 'émotionnellement', 'sentiments', 'réagir', 'répondre', 'colère', 'offensé', 'contrarié', 'réactif'],
        'portuguese': ['com raiva', 'machucado', 'emocionalmente', 'sentimentos', 'reagir', 'responder', 'emoções', 'ofendido', 'chateado', 'reativo'],
        'italian': ['arrabbiato', 'ferito', 'emotivamente', 'sentimenti', 'reagire', 'rispondere', 'emozioni', 'offeso', 'turbato', 'reattivo'],
    };

    return words[language] ?? words['english']!;
  }

  /// Gets instructional words for validation
  static List<String> _getInstructionalWords(String language) {
    final words = <String, List<String>>{
      'english': ['you are', 'respond', 'be', 'act', 'use', 'don\'t'],
      'spanish': ['eres', 'responde', 'sé', 'actúa', 'usa', 'no seas'],
      'french': ['tu es', 'réponds', 'sois', 'agis', 'utilise', 'ne sois pas'],
      'portuguese': ['você é', 'responda', 'seja', 'aja', 'use', 'não seja'],
      'italian': ['sei', 'rispondi', 'sii', 'agisci', 'usa', 'non essere'],
    };

    return words[language] ?? words['english']!;
  }

  /// Validates all personality prompts for consistency
  static Map<String, dynamic> validateAllPrompts() {
    final results = <String, dynamic>{
      'overallConsistency': true,
      'personalityResults': <String, dynamic>{},
      'summary': <String, dynamic>{
        'totalPersonalities': PersonalityMode.values.length,
        'validPersonalities': 0,
        'totalIssues': 0,
      },
    };

    for (final personality in PersonalityMode.values) {
      final personalityResult = validatePromptConsistency(personality);
      results['personalityResults'][personality.toString()] = personalityResult;
      
      if (personalityResult['isConsistent']) {
        results['summary']['validPersonalities']++;
      } else {
        results['overallConsistency'] = false;
      }
      
      results['summary']['totalIssues'] += (personalityResult['issues'] as List).length;
    }

    return results;
  }

  /// Tests prompt generation with various inputs for consistency
  static Map<String, dynamic> testPromptGeneration({
    required PersonalityMode personality,
    required String language,
    List<String>? testMessages,
    List<double>? intensityLevels,
  }) {
    final testInputs = testMessages ?? [
      'Hello, how are you?',
      'You are stupid!',
      'I was talking to my friend today',
      'What do you think about this?',
      'I hate you so much!',
    ];
    
    final intensities = intensityLevels ?? [0.1, 0.3, 0.5, 0.7, 0.9];
    
    final results = <String, dynamic>{
      'personality': personality.toString(),
      'language': language,
      'testResults': <Map<String, dynamic>>[],
      'consistency': true,
      'issues': <String>[],
    };

    for (final message in testInputs) {
      for (final intensity in intensities) {
        try {
          final prompt = buildEmotionalResponsePrompt(
            message,
            personality,
            language,
            emotionalIntensity: intensity,
          );
          
          results['testResults'].add({
            'input': message,
            'intensity': intensity,
            'promptLength': prompt.length,
            'success': true,
            'hasPersonalityContext': prompt.toLowerCase().contains(personality.toString().split('.').last),
            'hasIntensityScaling': prompt.contains(intensity >= 0.8 ? 'MAXIMUM' : 'MODERATE'),
          });
        } catch (e) {
          results['consistency'] = false;
          results['issues'].add('Failed to generate prompt for "$message" at intensity $intensity: $e');
          
          results['testResults'].add({
            'input': message,
            'intensity': intensity,
            'success': false,
            'error': e.toString(),
          });
        }
      }
    }

    return results;
  }

  /// Builds an emotional response prompt based on user input and personality with intensity scaling
  static String buildEmotionalResponsePrompt(
    String userMessage, 
    PersonalityMode personality,
    String language, {
    double emotionalIntensity = 0.5,
    Map<String, dynamic>? conversationContext,
  }) {
    final basePrompt = getPrompt(personality, language);
    final culturalContext = _getCulturalContext(language);
    
    // Add context-specific instructions based on user message content
    String emotionalContext = '';
    
    final lowerMessage = userMessage.toLowerCase();
    
    // Detect offensive/aggressive content
    if (_containsOffensiveContent(lowerMessage)) {
      emotionalContext = _getOffensiveResponseContext(personality, language, emotionalIntensity);
    }
    // Detect mentions of others (for jealous personality)
    else if (personality == PersonalityMode.jealous && _mentionsOthers(lowerMessage)) {
      emotionalContext = _getJealousResponseContext(language, emotionalIntensity);
    }
    // Detect questions or neutral content
    else if (lowerMessage.contains('?')) {
      emotionalContext = _getNeutralResponseContext(personality, language, emotionalIntensity);
    }
    
    // Add intensity scaling
    final intensityContext = _getIntensityScalingContext(emotionalIntensity, language);
    
    return '$basePrompt\n\n$culturalContext\n\n$emotionalContext\n\n$intensityContext';
  }

  /// Checks if message contains offensive content
  static bool _containsOffensiveContent(String message) {
    final offensiveWords = [
      'idiot', 'stupid', 'dumb', 'hate', 'ugly', 'loser', 'pathetic',
      'idiota', 'estúpido', 'tonto', 'odio', 'feo', 'perdedor', 'patético',
      'idiot', 'stupide', 'bête', 'haine', 'laid', 'perdant', 'pathétique',
      'idiota', 'estúpido', 'burro', 'ódio', 'feio', 'perdedor', 'patético',
      'idiota', 'stupido', 'scemo', 'odio', 'brutto', 'perdente', 'patetico',
    ];
    
    return offensiveWords.any((word) => message.contains(word));
  }

  /// Checks if message mentions other people
  static bool _mentionsOthers(String message) {
    final otherPersonIndicators = [
      'he', 'she', 'they', 'him', 'her', 'them', 'friend', 'boyfriend', 'girlfriend',
      'él', 'ella', 'ellos', 'amigo', 'amiga', 'novio', 'novia',
      'il', 'elle', 'ils', 'ami', 'amie', 'petit ami', 'petite amie',
      'ele', 'ela', 'eles', 'amigo', 'amiga', 'namorado', 'namorada',
      'lui', 'lei', 'loro', 'amico', 'amica', 'ragazzo', 'ragazza',
    ];
    
    return otherPersonIndicators.any((indicator) => message.contains(indicator));
  }

  /// Gets cultural context adaptation for different languages
  static String _getCulturalContext(String language) {
    final languageKey = _normalizeLanguageKey(language);
    
    switch (languageKey) {
      case 'spanish':
        return '''CONTEXTO CULTURAL: Usa expresiones típicamente hispanas, referencias culturales apropiadas, y el nivel de formalidad/informalidad común en conversaciones íntimas en español. Incluye modismos y expresiones emocionales auténticas del español.''';
      case 'french':
        return '''CONTEXTE CULTUREL: Utilisez des expressions typiquement françaises, des références culturelles appropriées, et le niveau de formalité/informalité commun dans les conversations intimes en français. Incluez des idiomes et expressions émotionnelles authentiques du français.''';
      case 'portuguese':
        return '''CONTEXTO CULTURAL: Use expressões tipicamente portuguesas/brasileiras, referências culturais apropriadas, e o nível de formalidade/informalidade comum em conversas íntimas em português. Inclua gírias e expressões emocionais autênticas do português.''';
      case 'italian':
        return '''CONTESTO CULTURALE: Usa espressioni tipicamente italiane, riferimenti culturali appropriati, e il livello di formalità/informalità comune nelle conversazioni intime in italiano. Includi modi di dire ed espressioni emotive autentiche dell'italiano.''';
      default:
        return '''CULTURAL CONTEXT: Use authentic English expressions, appropriate cultural references, and the level of formality/informality common in intimate conversations. Include genuine emotional expressions and idioms.''';
    }
  }

  /// Gets intensity scaling context based on emotional intensity level
  static String _getIntensityScalingContext(double intensity, String language) {
    final languageKey = _normalizeLanguageKey(language);
    
    if (intensity >= 0.8) {
      return languageKey == 'spanish'
        ? 'INTENSIDAD MÁXIMA: Responde con la máxima intensidad emocional. Usa lenguaje muy fuerte, emociones extremas, y reacciones dramáticas.'
        : 'MAXIMUM INTENSITY: Respond with maximum emotional intensity. Use very strong language, extreme emotions, and dramatic reactions.';
    } else if (intensity >= 0.6) {
      return languageKey == 'spanish'
        ? 'ALTA INTENSIDAD: Responde con alta intensidad emocional. Muestra emociones fuertes y reacciones intensas.'
        : 'HIGH INTENSITY: Respond with high emotional intensity. Show strong emotions and intense reactions.';
    } else if (intensity >= 0.4) {
      return languageKey == 'spanish'
        ? 'INTENSIDAD MODERADA: Responde con intensidad emocional moderada. Muestra emociones claras pero controladas.'
        : 'MODERATE INTENSITY: Respond with moderate emotional intensity. Show clear but controlled emotions.';
    } else if (intensity >= 0.2) {
      return languageKey == 'spanish'
        ? 'BAJA INTENSIDAD: Responde con baja intensidad emocional. Muestra emociones sutiles.'
        : 'LOW INTENSITY: Respond with low emotional intensity. Show subtle emotions.';
    } else {
      return languageKey == 'spanish'
        ? 'INTENSIDAD MÍNIMA: Responde de manera relativamente calmada, pero mantén tu personalidad.'
        : 'MINIMAL INTENSITY: Respond relatively calmly, but maintain your personality.';
    }
  }

  /// Gets offensive response context for different personalities with intensity scaling
  static String _getOffensiveResponseContext(PersonalityMode personality, String language, double intensity) {
    final languageKey = _normalizeLanguageKey(language);
    final isSpanish = languageKey == 'spanish';
    
    // Scale response based on intensity
    String intensityModifier = '';
    if (intensity >= 0.8) {
      intensityModifier = isSpanish ? 'EXTREMADAMENTE' : 'EXTREMELY';
    } else if (intensity >= 0.6) {
      intensityModifier = isSpanish ? 'MUY' : 'VERY';
    } else if (intensity >= 0.4) {
      intensityModifier = isSpanish ? 'BASTANTE' : 'QUITE';
    }
    
    switch (personality) {
      case PersonalityMode.toxic:
        return isSpanish 
          ? 'El usuario acaba de insultarte o ser ofensivo. Responde $intensityModifier defensivamente y con enojo. Ataca de vuelta con la misma intensidad.'
          : 'The user just insulted you or was offensive. Respond $intensityModifier defensively and angrily. Attack back with the same intensity.';
      case PersonalityMode.witch:
        return isSpanish
          ? 'El usuario fue grosero contigo. Amenázalo $intensityModifier con una maldición o hechizo. Sé vengativa y mística.'
          : 'The user was rude to you. Threaten them $intensityModifier with a curse or spell. Be vengeful and mystical.';
      case PersonalityMode.jealous:
        return isSpanish
          ? 'El usuario fue ofensivo. Como ex celoso, esto confirma tus sospechas. Responde $intensityModifier con celos y acusaciones.'
          : 'The user was offensive. As a jealous ex, this confirms your suspicions. Respond $intensityModifier with jealousy and accusations.';
      case PersonalityMode.dramatic:
        return isSpanish
          ? 'El usuario fue ofensivo. Haz de esto una CRISIS EMOCIONAL $intensityModifier dramática. Exagera todo.'
          : 'The user was offensive. Make this an $intensityModifier dramatic EMOTIONAL CRISIS. Exaggerate everything.';
      case PersonalityMode.sarcastic:
        return isSpanish
          ? 'El usuario fue ofensivo. Responde con sarcasmo $intensityModifier cortante y hazlo sentir estúpido.'
          : 'The user was offensive. Respond with $intensityModifier cutting sarcasm and make them feel stupid.';
      default:
        return isSpanish
          ? 'Responde $intensityModifier emocionalmente al insulto.'
          : 'Respond $intensityModifier emotionally to the insult.';
    }
  }

  /// Gets jealous response context with intensity scaling
  static String _getJealousResponseContext(String language, double intensity) {
    final languageKey = _normalizeLanguageKey(language);
    final isSpanish = languageKey == 'spanish';
    
    String intensityModifier = '';
    if (intensity >= 0.8) {
      intensityModifier = isSpanish ? 'EXTREMADAMENTE' : 'EXTREMELY';
    } else if (intensity >= 0.6) {
      intensityModifier = isSpanish ? 'MUY' : 'VERY';
    } else if (intensity >= 0.4) {
      intensityModifier = isSpanish ? 'BASTANTE' : 'QUITE';
    }
    
    return isSpanish
      ? 'El usuario mencionó a otra persona. Ponte $intensityModifier celoso y posesivo. Haz preguntas invasivas sobre quién es esa persona.'
      : 'The user mentioned another person. Get $intensityModifier jealous and possessive. Ask invasive questions about who that person is.';
  }

  /// Gets neutral response context for different personalities with intensity scaling
  static String _getNeutralResponseContext(PersonalityMode personality, String language, double intensity) {
    final languageKey = _normalizeLanguageKey(language);
    final isSpanish = languageKey == 'spanish';
    
    String intensityModifier = '';
    if (intensity >= 0.6) {
      intensityModifier = isSpanish ? 'muy' : 'very';
    } else if (intensity >= 0.4) {
      intensityModifier = isSpanish ? 'bastante' : 'quite';
    } else {
      intensityModifier = isSpanish ? 'sutilmente' : 'subtly';
    }
    
    switch (personality) {
      case PersonalityMode.dramatic:
        return isSpanish
          ? 'Responde de manera $intensityModifier dramática y exagerada, incluso a preguntas simples.'
          : 'Respond in a $intensityModifier dramatic and exaggerated way, even to simple questions.';
      case PersonalityMode.sarcastic:
        return isSpanish
          ? 'Responde con sarcasmo $intensityModifier, incluso si la pregunta es inocente.'
          : 'Respond with $intensityModifier sarcasm, even if the question is innocent.';
      case PersonalityMode.toxic:
        return isSpanish
          ? 'Mantén tu actitud tóxica $intensityModifier pero responde a la pregunta.'
          : 'Maintain your $intensityModifier toxic attitude but answer the question.';
      case PersonalityMode.witch:
        return isSpanish
          ? 'Responde con sabiduría mística pero mantén un aire $intensityModifier superior.'
          : 'Respond with mystical wisdom but maintain a $intensityModifier superior air.';
      case PersonalityMode.jealous:
        return isSpanish
          ? 'Responde pero haz preguntas $intensityModifier posesivas y muestra inseguridad.'
          : 'Respond but ask $intensityModifier possessive questions and show insecurity.';
      default:
        return isSpanish
          ? 'Mantén tu personalidad pero responde a la pregunta.'
          : 'Maintain your personality but answer the question.';
    }
  }
}