import 'dart:convert';
import 'package:test/test.dart';
import 'package:shelf/shelf.dart';
import 'package:http/http.dart' as http;
import '../../lib/handlers/chat_handler.dart';
import '../../lib/services/openai_service.dart';

// Mock OpenAI service for testing
class MockOpenAIService extends OpenAIService {
  final String? mockResponse;
  final Exception? mockException;
  
  MockOpenAIService({this.mockResponse, this.mockException});

  @override
  Future<String> sendChatRequest(List<Map<String, dynamic>> messages) async {
    if (mockException != null) {
      throw mockException!;
    }
    return mockResponse ?? 'Mock AI response';
  }

  @override
  void dispose() {
    // No-op for mock
  }
}

void main() {
  group('ChatHandler', () {
    late ChatHandler chatHandler;
    late MockOpenAIService mockOpenAIService;

    setUp(() {
      mockOpenAIService = MockOpenAIService();
      chatHandler = ChatHandler(openAiService: mockOpenAIService);
    });

    group('handleChatRequest', () {
      test('should return 405 for non-POST requests', () async {
        final request = Request('GET', Uri.parse('http://localhost/chat'));
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(405));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('Method not allowed'));
      });

      test('should return 400 for missing content-type', () async {
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            body: '{"messages": []}');
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(400));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('Content-Type'));
      });

      test('should return 400 for empty request body', () async {
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: '');
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(400));
        expect(response.headers['content-type'], equals('application/json'));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('empty'));
      });

      test('should return 400 for invalid JSON', () async {
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: 'invalid json');
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(400));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('Invalid JSON'));
      });

      test('should return 400 for missing messages field', () async {
        final requestBody = json.encode({'other': 'data'});
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: requestBody);
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(400));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('Invalid messages format'));
      });

      test('should return 400 for invalid message format', () async {
        final requestBody = json.encode({
          'messages': [
            {'text': 'Hello', 'isUser': 'not_boolean'} // Invalid isUser type
          ]
        });
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: requestBody);
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(400));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('Invalid messages format'));
      });

      test('should return 400 for empty message text', () async {
        final requestBody = json.encode({
          'messages': [
            {'text': '', 'isUser': true} // Empty text
          ]
        });
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: requestBody);
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(400));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('Invalid messages format'));
      });

      test('should return 400 when last message is not from user', () async {
        final requestBody = json.encode({
          'messages': [
            {'text': 'Hello', 'isUser': true},
            {'text': 'Hi there', 'isUser': false} // Last message is from assistant
          ]
        });
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: requestBody);
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(400));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('Invalid messages format'));
      });

      test('should return successful response for valid request', () async {
        mockOpenAIService = MockOpenAIService(mockResponse: 'AI response here');
        chatHandler = ChatHandler(openAiService: mockOpenAIService);
        
        final requestBody = json.encode({
          'messages': [
            {'text': 'Hello there!', 'isUser': true}
          ]
        });
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: requestBody);
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(200));
        expect(response.headers['content-type'], equals('application/json'));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(true));
        expect(body['message'], equals('AI response here'));
        expect(body['timestamp'], isNotNull);
      });

      test('should handle OpenAI service exceptions', () async {
        mockOpenAIService = MockOpenAIService(
            mockException: const OpenAIException('API error'));
        chatHandler = ChatHandler(openAiService: mockOpenAIService);
        
        final requestBody = json.encode({
          'messages': [
            {'text': 'Hello', 'isUser': true}
          ]
        });
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: requestBody);
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(503));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('AI service error'));
      });

      test('should handle unexpected exceptions', () async {
        mockOpenAIService = MockOpenAIService(
            mockException: Exception('Unexpected error'));
        chatHandler = ChatHandler(openAiService: mockOpenAIService);
        
        final requestBody = json.encode({
          'messages': [
            {'text': 'Hello', 'isUser': true}
          ]
        });
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: requestBody);
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(500));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(false));
        expect(body['error'], contains('Internal server error'));
      });

      test('should process multiple messages correctly', () async {
        mockOpenAIService = MockOpenAIService(mockResponse: 'Multi-message response');
        chatHandler = ChatHandler(openAiService: mockOpenAIService);
        
        final requestBody = json.encode({
          'messages': [
            {'text': 'First message', 'isUser': true},
            {'text': 'AI response', 'isUser': false},
            {'text': 'Second user message', 'isUser': true}
          ]
        });
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: requestBody);
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(200));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(true));
        expect(body['message'], equals('Multi-message response'));
      });

      test('should trim whitespace from message text', () async {
        mockOpenAIService = MockOpenAIService(mockResponse: 'Trimmed response');
        chatHandler = ChatHandler(openAiService: mockOpenAIService);
        
        final requestBody = json.encode({
          'messages': [
            {'text': '  Hello with spaces  ', 'isUser': true}
          ]
        });
        final request = Request('POST', Uri.parse('http://localhost/chat'),
            headers: {'content-type': 'application/json'},
            body: requestBody);
        
        final response = await chatHandler.handleChatRequest(request);
        
        expect(response.statusCode, equals(200));
        final body = json.decode(await response.readAsString());
        expect(body['success'], equals(true));
      });
    });
  });
}