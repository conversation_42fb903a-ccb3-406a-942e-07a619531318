# Setup Instructions

## Configuring OpenAI API Key

Since we've removed the separate backend, you now need to configure your OpenAI API key directly in the Flutter app.

### Steps:

1. **Get your OpenAI API Key:**
   - Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
   - Create a new API key or use an existing one

2. **Configure the API Key:**
   - Open `lib/config/app_config.dart`
   - Replace `'your_openai_api_key_here'` with your actual OpenAI API key

```dart
static const String _openAiApiKey = 'sk-your-actual-api-key-here';
```

3. **Security Note:**
   - **⚠️ WARNING:** Storing API keys in source code is not recommended for production apps
   - Consider these alternatives for production:
     - Use environment variables with `flutter_dotenv` package
     - Use a backend proxy to handle OpenAI requests
     - Use secure storage packages like `flutter_secure_storage`

## Running the App

1. **Install dependencies:**
   ```bash
   flutter pub get
   ```

2. **Run the app:**
   ```bash
   flutter run
   ```

## What Changed

- ✅ Removed separate backend server requirement
- ✅ OpenAI API calls now happen directly from Flutter app
- ✅ No need to run a separate Dart server
- ✅ Simplified deployment (just the Flutter app)
- ✅ Same chat functionality with "toxic ex" personality

## Testing

The app will now communicate directly with OpenAI's API, so make sure you have:
- A valid OpenAI API key
- Internet connection
- Sufficient OpenAI API credits/quota

## File Structure Changes

- `lib/config/app_config.dart` - New: API key configuration
- `lib/services/openai_service.dart` - New: Direct OpenAI API integration
- `lib/services/chat_service.dart` - Updated: Now uses OpenAI service directly
- `backend/` folder - Can be deleted (no longer needed)
