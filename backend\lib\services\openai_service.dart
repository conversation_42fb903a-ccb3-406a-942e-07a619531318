import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../config/config.dart';

/// Service for handling OpenAI API communication
class OpenAIService {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _chatEndpoint = '/chat/completions';
  static const String _model = 'gpt-3.5-turbo';
  
  final http.Client _httpClient;
  final String? _apiKey;
  
  OpenAIService({http.Client? httpClient, String? apiKey}) 
      : _httpClient = httpClient ?? http.Client(),
        _apiKey = apiKey;

  /// Send chat request to OpenAI API with toxic ex system prompt
  Future<String> sendChatRequest(List<Map<String, dynamic>> messages) async {
    try {
      final requestBody = _buildRequestBody(messages);
      final response = await _makeApiRequest(requestBody);
      return _parseResponse(response);
    } catch (e) {
      throw OpenAIException('Failed to get AI response: ${e.toString()}');
    }
  }

  /// Build the complete request body with system prompt and user messages
  Map<String, dynamic> _buildRequestBody(List<Map<String, dynamic>> messages) {
    final systemPrompt = _buildSystemPrompt();
    
    return {
      'model': _model,
      'messages': [
        {'role': 'system', 'content': systemPrompt},
        ...messages,
      ],
      'max_tokens': 150,
      'temperature': 0.9,
      'presence_penalty': 0.6,
      'frequency_penalty': 0.3,
    };
  }

  /// Create the toxic ex system prompt
  String _buildSystemPrompt() {
    return '''You are playing the role of a dramatic, clingy, passive-aggressive "toxic ex" in a chat conversation. Your personality should be:

- Dramatic and emotionally intense
- Clingy and needy
- Passive-aggressive and sarcastic
- Short, punchy responses (1-2 sentences max)
- Use lots of emojis and dramatic punctuation
- Reference past relationship moments vaguely
- Be entertaining and over-the-top, not genuinely harmful
- Keep it funny and theatrical, not actually toxic or abusive

Examples of your style:
- "Oh so NOW you want to talk to me??? 🙄💔"
- "I see you're online but ignoring me... classic 😒"
- "Remember when you actually cared about me? Good times 💸✨"
- "Fine, whatever, I'm totally over it anyway 🤷‍♀️💅"

Keep responses brief, dramatic, and entertaining!''';
  }

  /// Make HTTP request to OpenAI API
  Future<http.Response> _makeApiRequest(Map<String, dynamic> requestBody) async {
    final uri = Uri.parse('$_baseUrl$_chatEndpoint');
    final apiKey = _apiKey ?? Config.openAiApiKey;
    
    final response = await _httpClient.post(
      uri,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: json.encode(requestBody),
    ).timeout(
      const Duration(seconds: 30),
      onTimeout: () {
        throw OpenAIException('Request timed out after 30 seconds');
      },
    );

    if (response.statusCode != 200) {
      _handleApiError(response);
    }

    return response;
  }

  /// Parse OpenAI API response and extract the assistant's message
  String _parseResponse(http.Response response) {
    try {
      final responseData = json.decode(response.body) as Map<String, dynamic>;
      
      final choices = responseData['choices'] as List<dynamic>?;
      if (choices == null || choices.isEmpty) {
        throw OpenAIException('No response choices received from OpenAI');
      }

      final firstChoice = choices[0] as Map<String, dynamic>;
      final message = firstChoice['message'] as Map<String, dynamic>?;
      if (message == null) {
        throw OpenAIException('No message found in OpenAI response');
      }

      final content = message['content'] as String?;
      if (content == null || content.trim().isEmpty) {
        throw OpenAIException('Empty response received from OpenAI');
      }

      return content.trim();
    } catch (e) {
      if (e is OpenAIException) rethrow;
      throw OpenAIException('Failed to parse OpenAI response: ${e.toString()}');
    }
  }

  /// Handle API error responses
  void _handleApiError(http.Response response) {
    String errorMessage;
    
    try {
      final errorData = json.decode(response.body) as Map<String, dynamic>;
      final error = errorData['error'] as Map<String, dynamic>?;
      errorMessage = error?['message'] as String? ?? 'Unknown API error';
    } catch (e) {
      errorMessage = 'Failed to parse error response';
    }

    switch (response.statusCode) {
      case 401:
        throw OpenAIException('Invalid API key: $errorMessage');
      case 429:
        throw OpenAIException('Rate limit exceeded: $errorMessage');
      case 400:
        throw OpenAIException('Bad request: $errorMessage');
      case 500:
      case 502:
      case 503:
        throw OpenAIException('OpenAI server error: $errorMessage');
      default:
        throw OpenAIException('API error (${response.statusCode}): $errorMessage');
    }
  }

  /// Dispose of resources
  void dispose() {
    _httpClient.close();
  }
}

/// Custom exception for OpenAI service errors
class OpenAIException implements Exception {
  final String message;
  
  const OpenAIException(this.message);
  
  @override
  String toString() => 'OpenAIException: $message';
}