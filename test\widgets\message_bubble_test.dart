import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/widgets/message_bubble.dart';

void main() {
  group('MessageBubble Widget Tests', () {
    late Message userMessage;
    late Message assistantMessage;

    setUp(() {
      userMessage = Message(
        text: 'Hello, this is a user message!',
        isUser: true,
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      );

      assistantMessage = Message(
        text: 'Hi there! This is an assistant response.',
        isUser: false,
        timestamp: DateTime.now().subtract(const Duration(minutes: 3)),
      );
    });

    testWidgets('should display user message with correct styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: userMessage),
          ),
        ),
      );

      // Verify message text is displayed
      expect(find.text(userMessage.text), findsOneWidget);

      // Verify timestamp is displayed
      expect(find.text('5m ago'), findsOneWidget);

      // Verify the message bubble container exists
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('should display assistant message with correct styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: assistantMessage),
          ),
        ),
      );

      // Verify message text is displayed
      expect(find.text(assistantMessage.text), findsOneWidget);

      // Verify timestamp is displayed
      expect(find.text('3m ago'), findsOneWidget);

      // Verify the message bubble container exists
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('should show different alignment for user vs assistant messages', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                MessageBubble(message: userMessage),
                MessageBubble(message: assistantMessage),
              ],
            ),
          ),
        ),
      );

      // Both messages should be displayed
      expect(find.text(userMessage.text), findsOneWidget);
      expect(find.text(assistantMessage.text), findsOneWidget);

      // Verify different timestamps
      expect(find.text('5m ago'), findsOneWidget);
      expect(find.text('3m ago'), findsOneWidget);
    });

    testWidgets('should format timestamps correctly', (WidgetTester tester) async {
      final recentMessage = Message(
        text: 'Recent message',
        isUser: true,
        timestamp: DateTime.now().subtract(const Duration(seconds: 30)),
      );

      final hourOldMessage = Message(
        text: 'Hour old message',
        isUser: false,
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                MessageBubble(message: recentMessage),
                MessageBubble(message: hourOldMessage),
              ],
            ),
          ),
        ),
      );

      // Verify recent message shows "Just now"
      expect(find.text('Just now'), findsOneWidget);

      // Verify hour old message shows hours
      expect(find.text('2h ago'), findsOneWidget);
    });

    testWidgets('should apply animation when provided', (WidgetTester tester) async {
      final animationController = AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: tester,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(
              message: userMessage,
              animation: animationController,
            ),
          ),
        ),
      );

      // Verify SlideTransition and FadeTransition are present when animation is provided
      expect(find.byType(SlideTransition), findsOneWidget);
      expect(find.byType(FadeTransition), findsOneWidget);

      animationController.dispose();
    });

    testWidgets('should not apply animation when not provided', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: userMessage),
          ),
        ),
      );

      // Verify no animation widgets are present when animation is not provided
      expect(find.byType(SlideTransition), findsNothing);
      expect(find.byType(FadeTransition), findsNothing);
    });

    testWidgets('should display message text with correct styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: userMessage),
          ),
        ),
      );

      // Find the Text widget containing the message
      final textWidget = tester.widget<Text>(find.text(userMessage.text));
      
      // Verify text styling (accounting for responsive font sizing)
      expect(textWidget.style?.color, Colors.white);
      expect(textWidget.style?.fontSize, greaterThanOrEqualTo(16.0)); // Responsive font size
      expect(textWidget.style?.height, 1.4);
      expect(textWidget.style?.fontWeight, FontWeight.w400);
    });

    testWidgets('should have proper margin spacing', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                MessageBubble(message: userMessage),
                MessageBubble(message: assistantMessage),
              ],
            ),
          ),
        ),
      );

      // Verify both messages are rendered
      expect(find.text(userMessage.text), findsOneWidget);
      expect(find.text(assistantMessage.text), findsOneWidget);
    });
  });
}