import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/personality_mode.dart';
import 'package:toxic_chat/services/personality_prompts.dart';

void main() {
  group('PersonalityPrompts', () {
    test('should return prompts for all personality modes', () {
      for (final personality in PersonalityMode.values) {
        final prompt = PersonalityPrompts.getPrompt(personality, 'english');
        expect(prompt, isNotEmpty);
        expect(prompt, isA<String>());
      }
    });

    test('should return different prompts for different personalities', () {
      final toxicPrompt = PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'english');
      final witchPrompt = PersonalityPrompts.getPrompt(PersonalityMode.witch, 'english');
      
      expect(toxicPrompt, isNot(equals(witchPrompt)));
      expect(toxicPrompt, contains('toxic'));
      expect(witchPrompt, contains('witch'));
    });

    test('should support multiple languages', () {
      final englishPrompt = PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'english');
      final spanishPrompt = PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'spanish');
      
      expect(englishPrompt, isNot(equals(spanishPrompt)));
      expect(spanishPrompt, contains('tóxico'));
    });

    test('should fallback to English for unsupported languages', () {
      final englishPrompt = PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'english');
      final unsupportedPrompt = PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'unsupported');
      
      expect(unsupportedPrompt, equals(englishPrompt));
    });

    test('should return all available personalities', () {
      final personalities = PersonalityPrompts.getAllPersonalities();
      expect(personalities.length, equals(5));
      expect(personalities, contains(PersonalityMode.toxic));
      expect(personalities, contains(PersonalityMode.witch));
    });

    test('should return supported languages for personality', () {
      final languages = PersonalityPrompts.getSupportedLanguages(PersonalityMode.toxic);
      expect(languages, contains('english'));
      expect(languages, contains('spanish'));
      expect(languages, contains('french'));
    });

    test('should check language support correctly', () {
      expect(PersonalityPrompts.isLanguageSupported(PersonalityMode.toxic, 'english'), isTrue);
      expect(PersonalityPrompts.isLanguageSupported(PersonalityMode.toxic, 'spanish'), isTrue);
      expect(PersonalityPrompts.isLanguageSupported(PersonalityMode.toxic, 'french'), isTrue);
      // Even unsupported languages return true because they fallback to English
      expect(PersonalityPrompts.isLanguageSupported(PersonalityMode.toxic, 'chinese'), isTrue);
    });

    test('should build emotional response prompts', () {
      final prompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'You are an idiot!',
        PersonalityMode.toxic,
        'english',
      );
      
      expect(prompt, isNotEmpty);
      expect(prompt, contains('toxic'));
    });

    test('should build emotional response prompts with intensity scaling', () {
      final lowIntensityPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'You are an idiot!',
        PersonalityMode.toxic,
        'english',
        emotionalIntensity: 0.2,
      );
      
      final highIntensityPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'You are an idiot!',
        PersonalityMode.toxic,
        'english',
        emotionalIntensity: 0.9,
      );
      
      expect(lowIntensityPrompt, isNotEmpty);
      expect(highIntensityPrompt, isNotEmpty);
      expect(lowIntensityPrompt, isNot(equals(highIntensityPrompt)));
      expect(highIntensityPrompt, contains('MAXIMUM'));
      expect(lowIntensityPrompt, contains('LOW'));
    });

    test('should include cultural context in prompts', () {
      final englishPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'Hello',
        PersonalityMode.toxic,
        'english',
      );
      
      final spanishPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'Hola',
        PersonalityMode.toxic,
        'spanish',
      );
      
      expect(englishPrompt, contains('CULTURAL'));
      expect(spanishPrompt, contains('CULTURAL'));
      expect(spanishPrompt, contains('hispanas'));
    });

    test('should detect offensive content', () {
      final offensivePrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'You are stupid!',
        PersonalityMode.toxic,
        'english',
      );
      
      final neutralPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'How are you?',
        PersonalityMode.toxic,
        'english',
      );
      
      expect(offensivePrompt, isNot(equals(neutralPrompt)));
    });

    test('should handle jealous personality with mentions of others', () {
      final jealousPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'I was talking to my friend today',
        PersonalityMode.jealous,
        'english',
      );
      
      expect(jealousPrompt, contains('jealous'));
    });

    test('should normalize language keys correctly', () {
      // Test various language code formats
      expect(PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'en'), 
             equals(PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'english')));
      expect(PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'es'), 
             equals(PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'spanish')));
    });
  });
}