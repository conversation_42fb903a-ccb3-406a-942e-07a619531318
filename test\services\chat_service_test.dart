import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:http/testing.dart';
import '../../lib/models/message.dart';
import '../../lib/services/chat_service.dart';

void main() {
  group('ChatService', () {
    late ChatService chatService;
    late MockClient mockClient;

    setUp(() {
      mockClient = MockClient((request) async {
        return http.Response('{"success": true, "message": "Test response"}', 200);
      });
      chatService = ChatService(client: mockClient);
    });

    tearDown(() {
      chatService.dispose();
    });

    group('sendMessage', () {
      test('should send message and return AI response', () async {
        // Arrange
        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        mockClient = MockClient((request) async {
          // Verify request
          expect(request.method, 'POST');
          expect(request.url.toString(), 'http://localhost:8080/chat');
          expect(request.headers['Content-Type'], contains('application/json'));
          expect(request.headers['Accept'], 'application/json');

          // Verify request body
          final requestBody = json.decode(request.body) as Map<String, dynamic>;
          expect(requestBody['messages'], isA<List>());
          expect(requestBody['messages'].length, 1);
          expect(requestBody['messages'][0]['text'], 'Hello');
          expect(requestBody['messages'][0]['isUser'], true);

          return http.Response(
            json.encode({
              'success': true,
              'message': 'Hello there!',
              'timestamp': DateTime.now().toIso8601String(),
            }),
            200,
          );
        });

        chatService = ChatService(client: mockClient);

        // Act
        final result = await chatService.sendMessage(messages);

        // Assert
        expect(result.text, 'Hello there!');
        expect(result.isUser, false);
        expect(result.role, 'assistant');
        expect(result.timestamp, isA<DateTime>());
      });

      test('should handle multiple messages in conversation history', () async {
        // Arrange
        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
            role: 'user',
          ),
          Message(
            text: 'Hi there!',
            isUser: false,
            timestamp: DateTime.now().subtract(const Duration(minutes: 4)),
            role: 'assistant',
          ),
          Message(
            text: 'How are you?',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        mockClient = MockClient((request) async {
          final requestBody = json.decode(request.body) as Map<String, dynamic>;
          expect(requestBody['messages'].length, 3);
          
          return http.Response(
            json.encode({
              'success': true,
              'message': 'I am doing well, thanks!',
            }),
            200,
          );
        });

        chatService = ChatService(client: mockClient);

        // Act
        final result = await chatService.sendMessage(messages);

        // Assert
        expect(result.text, 'I am doing well, thanks!');
        expect(result.isUser, false);
      });

      test('should throw exception when messages list is empty', () async {
        // Act & Assert
        expect(
          () => chatService.sendMessage([]),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            'Messages list cannot be empty',
          )),
        );
      });

      test('should throw exception when last message is not from user', () async {
        // Arrange
        final messages = [
          Message(
            text: 'Hello',
            isUser: false,
            timestamp: DateTime.now(),
            role: 'assistant',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            'Last message must be from user',
          )),
        );
      });

      test('should handle network connection errors', () async {
        // Arrange
        mockClient = MockClient((request) async {
          throw const SocketException('No Internet connection');
        });
        chatService = ChatService(client: mockClient);

        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            'Unable to connect to chat service. Please check your connection.',
          )),
        );
      });

      test('should handle HTTP client exceptions', () async {
        // Arrange
        mockClient = MockClient((request) async {
          throw http.ClientException('Connection timeout');
        });
        chatService = ChatService(client: mockClient);

        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            contains('Network error: Connection timeout'),
          )),
        );
      });

      test('should handle HTTP error status codes', () async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'success': false,
              'error': 'Invalid request format',
            }),
            400,
          );
        });
        chatService = ChatService(client: mockClient);

        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            'Invalid request format',
          )),
        );
      });

      test('should handle server error responses', () async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'success': false,
              'error': 'AI service error: Rate limit exceeded',
            }),
            503,
          );
        });
        chatService = ChatService(client: mockClient);

        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            'AI service error: Rate limit exceeded',
          )),
        );
      });

      test('should handle invalid JSON response', () async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response('Invalid JSON', 200);
        });
        chatService = ChatService(client: mockClient);

        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            'Invalid response format from server',
          )),
        );
      });

      test('should handle empty AI response', () async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'success': true,
              'message': '',
            }),
            200,
          );
        });
        chatService = ChatService(client: mockClient);

        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            'Empty response from AI service',
          )),
        );
      });

      test('should use custom base URL and timeout', () async {
        // Arrange
        const customBaseUrl = 'http://custom-host:9000';
        const customTimeout = Duration(seconds: 10);
        
        mockClient = MockClient((request) async {
          expect(request.url.toString(), '$customBaseUrl/chat');
          return http.Response(
            json.encode({
              'success': true,
              'message': 'Custom response',
            }),
            200,
          );
        });

        chatService = ChatService(
          baseUrl: customBaseUrl,
          timeout: customTimeout,
          client: mockClient,
        );

        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        // Act
        final result = await chatService.sendMessage(messages);

        // Assert
        expect(result.text, 'Custom response');
      });
    });

    group('isServiceAvailable', () {
      test('should return true when health check succeeds', () async {
        // Arrange
        mockClient = MockClient((request) async {
          expect(request.url.toString(), 'http://localhost:8080/health');
          return http.Response('{"status": "healthy"}', 200);
        });
        chatService = ChatService(client: mockClient);

        // Act
        final result = await chatService.isServiceAvailable();

        // Assert
        expect(result, true);
      });

      test('should return false when health check fails', () async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response('Service unavailable', 503);
        });
        chatService = ChatService(client: mockClient);

        // Act
        final result = await chatService.isServiceAvailable();

        // Assert
        expect(result, false);
      });

      test('should return false when network error occurs', () async {
        // Arrange
        mockClient = MockClient((request) async {
          throw const SocketException('No connection');
        });
        chatService = ChatService(client: mockClient);

        // Act
        final result = await chatService.isServiceAvailable();

        // Assert
        expect(result, false);
      });
    });

    group('error message extraction', () {
      test('should extract error message from response body', () async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response(
            json.encode({
              'success': false,
              'error': 'Custom error message',
            }),
            400,
          );
        });
        chatService = ChatService(client: mockClient);

        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            'Custom error message',
          )),
        );
      });

      test('should use default error message for status codes', () async {
        // Arrange
        mockClient = MockClient((request) async {
          return http.Response('Internal Server Error', 500);
        });
        chatService = ChatService(client: mockClient);

        final messages = [
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now(),
            role: 'user',
          ),
        ];

        // Act & Assert
        expect(
          () => chatService.sendMessage(messages),
          throwsA(isA<ChatServiceException>().having(
            (e) => e.message,
            'message',
            'Internal server error. Please try again later.',
          )),
        );
      });
    });

    group('ChatServiceException', () {
      test('should create exception with message only', () {
        const exception = ChatServiceException('Test error');
        expect(exception.message, 'Test error');
        expect(exception.statusCode, null);
        expect(exception.toString(), 'ChatServiceException: Test error');
      });

      test('should create exception with message and status code', () {
        const exception = ChatServiceException('Test error', 400);
        expect(exception.message, 'Test error');
        expect(exception.statusCode, 400);
        expect(exception.toString(), 'ChatServiceException: Test error');
      });
    });
  });
}