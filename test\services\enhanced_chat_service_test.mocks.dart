// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in toxic_chat/test/services/enhanced_chat_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;
import 'package:toxic_chat/models/personality_mode.dart' as _i4;
import 'package:toxic_chat/services/openai_service.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [OpenAIService].
///
/// See the documentation for Mockito's code generation for more information.
class MockOpenAIService extends _i1.Mock implements _i2.OpenAIService {
  MockOpenAIService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<String> sendChatRequest(
    List<Map<String, dynamic>>? messages, {
    _i4.PersonalityMode? personality = _i4.PersonalityMode.toxic,
    String? language = 'english',
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #sendChatRequest,
              [messages],
              {#personality: personality, #language: language},
            ),
            returnValue: _i3.Future<String>.value(
              _i5.dummyValue<String>(
                this,
                Invocation.method(
                  #sendChatRequest,
                  [messages],
                  {#personality: personality, #language: language},
                ),
              ),
            ),
          )
          as _i3.Future<String>);

  @override
  String buildEmotionalResponsePrompt(
    String? userMessage,
    _i4.PersonalityMode? personality,
    String? language,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#buildEmotionalResponsePrompt, [
              userMessage,
              personality,
              language,
            ]),
            returnValue: _i5.dummyValue<String>(
              this,
              Invocation.method(#buildEmotionalResponsePrompt, [
                userMessage,
                personality,
                language,
              ]),
            ),
          )
          as String);

  @override
  Map<String, String> createLocalizedResponseTemplates(
    _i4.PersonalityMode? personality,
    String? language,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createLocalizedResponseTemplates, [
              personality,
              language,
            ]),
            returnValue: <String, String>{},
          )
          as Map<String, String>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
