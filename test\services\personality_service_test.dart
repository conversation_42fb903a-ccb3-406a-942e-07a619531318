import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:toxic_chat/models/personality_mode.dart';
import 'package:toxic_chat/models/personality_configuration.dart';
import 'package:toxic_chat/models/message.dart';
import 'package:toxic_chat/services/personality_service.dart';

void main() {
  group('PersonalityService', () {
    late PersonalityService service;

    setUp(() async {
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      service = PersonalityService();
    });

    test('should initialize with default configuration', () {
      expect(service.currentConfig.currentPersonality, equals(PersonalityMode.toxic));
      expect(service.currentConfig.enableEmotionalResponses, isTrue);
      expect(service.currentConfig.emotionalIntensity, equals(0.8));
    });

    test('should load and save configuration', () async {
      final newConfig = PersonalityConfiguration(
        currentPersonality: PersonalityMode.witch,
        emotionalIntensity: 0.6,
        preferredLanguage: 'spanish',
      );

      await service.updateConfiguration(newConfig);
      expect(service.currentConfig.currentPersonality, equals(PersonalityMode.witch));
      expect(service.currentConfig.emotionalIntensity, equals(0.6));
      expect(service.currentConfig.preferredLanguage, equals('spanish'));

      // Create new service instance to test loading
      final newService = PersonalityService();
      await newService.loadConfiguration();
      expect(newService.currentConfig.currentPersonality, equals(PersonalityMode.witch));
      expect(newService.currentConfig.emotionalIntensity, equals(0.6));
    });

    test('should change personality mode', () async {
      await service.changePersonality(PersonalityMode.sarcastic);
      expect(service.currentConfig.currentPersonality, equals(PersonalityMode.sarcastic));
    });

    test('should set emotional intensity', () async {
      await service.setEmotionalIntensity(0.3);
      expect(service.currentConfig.emotionalIntensity, equals(0.3));
    });

    test('should not set invalid emotional intensity', () async {
      final originalIntensity = service.currentConfig.emotionalIntensity;
      await service.setEmotionalIntensity(-0.1);
      expect(service.currentConfig.emotionalIntensity, equals(originalIntensity));
      
      await service.setEmotionalIntensity(1.5);
      expect(service.currentConfig.emotionalIntensity, equals(originalIntensity));
    });

    test('should toggle emotional responses', () async {
      final original = service.currentConfig.enableEmotionalResponses;
      await service.toggleEmotionalResponses();
      expect(service.currentConfig.enableEmotionalResponses, equals(!original));
    });

    test('should set preferred language', () async {
      await service.setPreferredLanguage('french');
      expect(service.currentConfig.preferredLanguage, equals('french'));
    });

    test('should toggle auto detect language', () async {
      final original = service.currentConfig.autoDetectLanguage;
      await service.toggleAutoDetectLanguage();
      expect(service.currentConfig.autoDetectLanguage, equals(!original));
    });

    test('should generate system prompt', () {
      final prompt = service.generateSystemPrompt('english');
      expect(prompt, isNotEmpty);
      expect(prompt, contains('toxic'));
    });

    test('should generate basic prompt when emotional responses disabled', () async {
      await service.toggleEmotionalResponses(); // Disable
      final prompt = service.generateSystemPrompt('english');
      expect(prompt, contains('helpful AI assistant'));
    });

    test('should generate emotional prompt', () {
      final prompt = service.generateEmotionalPrompt('You are stupid!', 'english');
      expect(prompt, isNotEmpty);
      expect(prompt, contains('toxic'));
    });

    test('should use detected language when auto detect is enabled', () {
      final englishPrompt = service.generateSystemPrompt('english');
      final spanishPrompt = service.generateSystemPrompt('spanish');
      expect(englishPrompt, isNot(equals(spanishPrompt)));
    });

    test('should use preferred language when auto detect is disabled', () async {
      await service.setPreferredLanguage('spanish');
      await service.toggleAutoDetectLanguage(); // Disable auto detect
      
      final prompt1 = service.generateSystemPrompt('english');
      final prompt2 = service.generateSystemPrompt('french');
      // Both should use Spanish since auto detect is off
      expect(prompt1, equals(prompt2));
      expect(prompt1, contains('tóxico'));
    });

    test('should get available personalities', () {
      final personalities = service.getAvailablePersonalities();
      expect(personalities.length, equals(5));
      expect(personalities, contains(PersonalityMode.toxic));
      expect(personalities, contains(PersonalityMode.witch));
    });

    test('should check language support', () {
      expect(service.isLanguageSupported('english'), isTrue);
      expect(service.isLanguageSupported('spanish'), isTrue);
    });

    test('should get supported languages', () {
      final languages = service.getSupportedLanguages();
      expect(languages, contains('english'));
      expect(languages, contains('spanish'));
    });

    test('should reset to default configuration', () async {
      // Change some settings
      await service.changePersonality(PersonalityMode.witch);
      await service.setEmotionalIntensity(0.3);
      
      // Reset
      await service.resetToDefault();
      
      expect(service.currentConfig.currentPersonality, equals(PersonalityMode.toxic));
      expect(service.currentConfig.emotionalIntensity, equals(0.8));
    });

    test('should provide configuration description', () {
      final description = service.getConfigurationDescription();
      expect(description, isNotEmpty);
      expect(description, contains('Toxic'));
    });

    test('should validate configuration', () {
      expect(service.isConfigurationValid(), isTrue);
    });

    test('should handle invalid configuration gracefully', () async {
      // Try to update with invalid config
      final invalidConfig = PersonalityConfiguration(emotionalIntensity: -0.5);
      await service.updateConfiguration(invalidConfig);
      
      // Should not update to invalid config
      expect(service.currentConfig.emotionalIntensity, isNot(equals(-0.5)));
    });

    test('should handle SharedPreferences errors gracefully', () async {
      // This test ensures the service doesn't crash if SharedPreferences fails
      // The actual error handling is internal, so we just verify it doesn't throw
      expect(() async => await service.loadConfiguration(), returnsNormally);
      expect(() async => await service.saveConfiguration(), returnsNormally);
    });

    group('Enhanced Emotional Response Methods', () {
      test('should generate enhanced emotional prompt with conversation history', () {
        final history = <Message>[
          Message(
            text: 'Hello',
            isUser: true,
            timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
          ),
          Message(
            text: 'Hi there',
            isUser: false,
            timestamp: DateTime.now().subtract(const Duration(minutes: 4)),
          ),
        ];

        final prompt = service.generateEnhancedEmotionalPrompt(
          'You are an idiot!',
          'english',
          history,
        );

        expect(prompt, isNotEmpty);
        expect(prompt, contains('toxic'));
      });

      test('should detect emotional intensity', () {
        final intensity = service.detectEmotionalIntensity('You are stupid!', 'english');
        expect(intensity, greaterThan(0.0));
        expect(intensity, lessThanOrEqualTo(1.0));
      });

      test('should detect offensive content', () {
        expect(service.containsOffensiveContent('You are an idiot', 'english'), isTrue);
        expect(service.containsOffensiveContent('Hello there', 'english'), isFalse);
      });

      test('should detect aggressive language', () {
        expect(service.containsAggressiveLanguage('You always do this!', 'english'), isTrue);
        expect(service.containsAggressiveLanguage('How are you?', 'english'), isFalse);
      });

      test('should detect mentions of other people', () {
        expect(service.mentionsOtherPeople('I was with my friend', 'english'), isTrue);
        expect(service.mentionsOtherPeople('I went shopping', 'english'), isFalse);
      });

      test('should respect language preferences in emotional detection', () async {
        await service.setPreferredLanguage('spanish');
        await service.toggleAutoDetectLanguage(); // Disable auto detect

        final hasOffensive = service.containsOffensiveContent('idiota', 'english');
        expect(hasOffensive, isTrue); // Should use Spanish detection
      });

      test('should use auto-detected language when enabled', () async {
        await service.toggleAutoDetectLanguage(); // Enable auto detect if not already

        final intensity1 = service.detectEmotionalIntensity('idiot', 'english');
        final intensity2 = service.detectEmotionalIntensity('idiota', 'spanish');

        expect(intensity1, greaterThan(0.0));
        expect(intensity2, greaterThan(0.0));
      });

      test('should return basic prompt when emotional responses disabled', () async {
        await service.toggleEmotionalResponses(); // Disable

        final prompt = service.generateEnhancedEmotionalPrompt(
          'You are terrible!',
          'english',
          [],
        );

        expect(prompt, contains('helpful AI assistant'));
      });
    });
  });
}