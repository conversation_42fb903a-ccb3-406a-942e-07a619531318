import 'package:dotenv/dotenv.dart';

class Config {
  static late DotEnv _env;
  static bool _initialized = false;

  /// Initialize configuration by loading environment variables
  static void initialize() {
    if (_initialized) return;
    
    _env = DotEnv(includePlatformEnvironment: true);
    _env.load();
    _initialized = true;
  }

  /// Get OpenAI API key
  static String get openAiApiKey {
    _ensureInitialized();
    final key = _env['OPENAI_API_KEY'];
    if (key == null || key.isEmpty || key == 'your_openai_api_key_here') {
      throw Exception('OPENAI_API_KEY not configured in .env file');
    }
    return key;
  }

  /// Get server port
  static int get port {
    _ensureInitialized();
    return int.parse(_env['PORT'] ?? '8080');
  }

  /// Get server host
  static String get host {
    _ensureInitialized();
    return _env['HOST'] ?? 'localhost';
  }

  /// Check if configuration is properly set up
  static bool get isConfigured {
    try {
      _ensureInitialized();
      final key = _env['OPENAI_API_KEY'];
      return key != null && key.isNotEmpty && key != 'your_openai_api_key_here';
    } catch (e) {
      return false;
    }
  }

  static void _ensureInitialized() {
    if (!_initialized) {
      throw Exception('Config not initialized. Call Config.initialize() first.');
    }
  }
}