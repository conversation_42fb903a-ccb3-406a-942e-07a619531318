import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:toxic_chat/providers/theme_provider.dart';
import 'package:toxic_chat/screens/chat_screen.dart';

void main() {
  group('Enhanced Chat Styling Integration Tests', () {
    testWidgets('should render chat screen', (WidgetTester tester) async {
      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const MaterialApp(home: ChatScreen()),
        ),
      );

      // Verify the app bar is rendered
      expect(find.text('Toxic Chat'), findsOneWidget);

      // Verify the settings button is present
      expect(find.byIcon(Icons.settings), findsOneWidget);

      // Verify empty state is shown initially
      expect(find.text('Start a conversation'), findsOneWidget);
    });

    testWidgets('should maintain responsive design', (
      WidgetTester tester,
    ) async {
      // Test with different screen sizes
      await tester.binding.setSurfaceSize(const Size(400, 800)); // Mobile
      await tester.pumpWidget(
        ChangeNotifierProvider(
          create: (_) => ThemeProvider(),
          child: const MaterialApp(home: ChatScreen()),
        ),
      );

      expect(find.byType(ChatScreen), findsOneWidget);

      // Test with tablet size
      await tester.binding.setSurfaceSize(const Size(800, 1200)); // Tablet
      await tester.pumpAndSettle();

      expect(find.byType(ChatScreen), findsOneWidget);

      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });
  });
}
