import 'package:flutter/material.dart';
import 'lib/models/message.dart';
import 'lib/widgets/message_bubble.dart';

/// Example demonstrating message animations
/// Run this with: flutter run example_message_animations.dart
void main() {
  runApp(const MessageAnimationExample());
}

class MessageAnimationExample extends StatelessWidget {
  const MessageAnimationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Message Animation Demo',
      theme: ThemeData.dark(),
      home: const AnimationDemoScreen(),
    );
  }
}

class AnimationDemoScreen extends StatefulWidget {
  const AnimationDemoScreen({super.key});

  @override
  State<AnimationDemoScreen> createState() => _AnimationDemoScreenState();
}

class _AnimationDemoScreenState extends State<AnimationDemoScreen>
    with TickerProviderStateMixin {
  final List<Message> _messages = [];
  final Map<int, AnimationController> _animationControllers = {};
  int _messageCounter = 0;

  @override
  void dispose() {
    for (final controller in _animationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _addMessage(bool isUser) {
    final message = Message(
      text: isUser 
          ? 'User message ${_messageCounter + 1}' 
          : 'AI response to message ${_messageCounter + 1}',
      isUser: isUser,
      timestamp: DateTime.now(),
      role: isUser ? 'user' : 'assistant',
    );

    setState(() {
      _messages.add(message);
      _messageCounter++;
    });

    // Create animation controller
    final controller = AnimationController(
      duration: Duration(milliseconds: isUser ? 250 : 400),
      vsync: this,
    );
    
    _animationControllers[_messages.length - 1] = controller;
    
    // Start animation with delay for AI messages
    final delay = isUser ? 0 : 150;
    Future.delayed(Duration(milliseconds: delay), () {
      if (mounted) {
        controller.forward();
      }
    });
  }

  void _clearMessages() {
    setState(() {
      _messages.clear();
      _messageCounter = 0;
    });
    
    // Dispose old controllers
    for (final controller in _animationControllers.values) {
      controller.dispose();
    }
    _animationControllers.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      appBar: AppBar(
        title: const Text('Message Animation Demo'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF6B73FF), Color(0xFF9B59B6)],
            ),
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF1A1A1A), Color(0xFF2D2D2D)],
          ),
        ),
        child: Column(
          children: [
            // Control buttons
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => _addMessage(true),
                    icon: const Icon(Icons.person),
                    label: const Text('Add User Message'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6B73FF),
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _addMessage(false),
                    icon: const Icon(Icons.smart_toy),
                    label: const Text('Add AI Message'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF9B59B6),
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: _clearMessages,
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[700],
                    ),
                  ),
                ],
              ),
            ),
            
            // Messages list
            Expanded(
              child: _messages.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.animation,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Tap buttons above to see animations',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];
                        final animation = _animationControllers[index];

                        return MessageBubble(
                          message: message,
                          animation: animation,
                        );
                      },
                    ),
            ),
            
            // Info panel
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.withValues(alpha: 0.3),
                  ),
                ),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Animation Features:',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• User messages slide in from the right (250ms)',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                  Text(
                    '• AI messages slide in from the left (400ms)',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                  Text(
                    '• AI messages have a 150ms delay for smooth transitions',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                  Text(
                    '• All messages include fade, slide, and scale animations',
                    style: TextStyle(color: Colors.grey, fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}