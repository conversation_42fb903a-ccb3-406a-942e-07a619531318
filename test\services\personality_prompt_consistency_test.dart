import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/personality_mode.dart';
import 'package:toxic_chat/services/personality_prompts.dart';

void main() {
  group('PersonalityPrompts Consistency Testing Framework', () {
    test('should validate all personality prompts for consistency', () {
      final results = PersonalityPrompts.validateAllPrompts();
      
      // Check that validation runs without errors
      expect(results, isNotNull);
      expect(results['personalityResults'], isNotEmpty);
      expect(results['summary']['totalPersonalities'], equals(PersonalityMode.values.length));
      
      // Print results for debugging if needed
      if (!results['overallConsistency']) {
        print('Validation issues found: ${results['summary']}');
      }
    });

    test('should validate individual personality prompt consistency', () {
      for (final personality in PersonalityMode.values) {
        final results = PersonalityPrompts.validatePromptConsistency(personality);
        
        // Check that validation runs and returns expected structure
        expect(results, isNotNull);
        expect(results['personality'], contains(personality.toString()));
        expect(results['languageAnalysis'], isNotEmpty);
        
        // Check that all supported languages are analyzed
        final languageAnalysis = results['languageAnalysis'] as Map<String, dynamic>;
        expect(languageAnalysis.keys, contains('english'));
        expect(languageAnalysis.keys, contains('spanish'));
        
        // Print issues for debugging if needed
        if (!results['isConsistent']) {
          print('Personality $personality issues: ${results['issues']}');
        }
      }
    });

    test('should validate prompt quality for each language', () {
      for (final personality in PersonalityMode.values) {
        final supportedLanguages = PersonalityPrompts.getSupportedLanguages(personality);
        
        for (final language in supportedLanguages) {
          final prompt = PersonalityPrompts.getPrompt(personality, language);
          
          // Basic quality checks
          expect(prompt.length, greaterThan(50), 
            reason: 'Prompt too short for $personality in $language');
          expect(prompt.split(' ').length, greaterThan(20), 
            reason: 'Prompt has too few words for $personality in $language');
          
          // Check that prompt is not empty or placeholder
          expect(prompt.trim(), isNotEmpty);
          expect(prompt, isNot(contains('[placeholder]')));
          expect(prompt, isNot(contains('TODO')));
          
          // Check for basic personality-related content (more flexible)
          final lowerPrompt = prompt.toLowerCase();
          final personalityName = personality.toString().split('.').last;
          
          // At minimum, should contain some personality-related terms
          expect(lowerPrompt.length, greaterThan(100), 
            reason: 'Prompt should be substantial for $personality in $language');
        }
      }
    });

    test('should generate consistent emotional response prompts', () {
      final testMessages = [
        'Hello, how are you?',
        'You are stupid!',
        'I was talking to my friend today',
        'What do you think about this?',
        'I hate you so much!',
      ];
      
      final intensityLevels = [0.1, 0.3, 0.5, 0.7, 0.9];
      
      for (final personality in PersonalityMode.values) {
        for (final language in ['english', 'spanish']) {
          final results = PersonalityPrompts.testPromptGeneration(
            personality: personality,
            language: language,
            testMessages: testMessages,
            intensityLevels: intensityLevels,
          );
          
          expect(results['consistency'], isTrue, 
            reason: 'Prompt generation inconsistent for $personality in $language: ${results['issues']}');
          expect(results['issues'], isEmpty);
          
          final testResults = results['testResults'] as List<Map<String, dynamic>>;
          expect(testResults.length, equals(testMessages.length * intensityLevels.length));
          
          // Check that all tests succeeded
          for (final testResult in testResults) {
            expect(testResult['success'], isTrue, 
              reason: 'Failed test: ${testResult['input']} at intensity ${testResult['intensity']}');
          }
        }
      }
    });

    test('should scale emotional intensity correctly', () {
      const testMessage = 'You are an idiot!';
      const personality = PersonalityMode.toxic;
      const language = 'english';
      
      final lowIntensityPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        testMessage, personality, language, emotionalIntensity: 0.2
      );
      
      final highIntensityPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        testMessage, personality, language, emotionalIntensity: 0.9
      );
      
      // High intensity should contain stronger language
      expect(highIntensityPrompt.toLowerCase(), contains('maximum'));
      expect(lowIntensityPrompt.toLowerCase(), contains('low'));
      expect(highIntensityPrompt.length, greaterThan(lowIntensityPrompt.length));
    });

    test('should include cultural context for different languages', () {
      const personality = PersonalityMode.toxic;
      
      final englishPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'Hello', personality, 'english'
      );
      
      final spanishPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'Hola', personality, 'spanish'
      );
      
      // Should contain cultural context
      expect(englishPrompt.toLowerCase(), contains('cultural'));
      expect(spanishPrompt.toLowerCase(), contains('cultural'));
      
      // Spanish should contain Spanish cultural references
      expect(spanishPrompt, contains('hispanas'));
      expect(spanishPrompt, contains('español'));
    });

    test('should handle offensive content detection correctly', () {
      const personality = PersonalityMode.toxic;
      const language = 'english';
      
      final offensivePrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'You are stupid!', personality, language
      );
      
      final neutralPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'How are you?', personality, language
      );
      
      // Offensive prompt should contain defensive instructions
      expect(offensivePrompt.toLowerCase(), contains('defensive'));
      expect(offensivePrompt.toLowerCase(), contains('attack'));
      
      // Neutral prompt should be calmer
      expect(neutralPrompt, isNot(contains('attack')));
    });

    test('should handle jealous personality mentions correctly', () {
      const personality = PersonalityMode.jealous;
      const language = 'english';
      
      final jealousPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'I was talking to my friend today', personality, language
      );
      
      final neutralPrompt = PersonalityPrompts.buildEmotionalResponsePrompt(
        'How are you?', personality, language
      );
      
      // Jealous prompt should contain possessive instructions
      expect(jealousPrompt.toLowerCase(), contains('jealous'));
      expect(jealousPrompt.toLowerCase(), contains('possessive'));
      
      // Should be different from neutral
      expect(jealousPrompt, isNot(equals(neutralPrompt)));
    });

    test('should maintain consistency across language translations', () {
      for (final personality in PersonalityMode.values) {
        final englishPrompt = PersonalityPrompts.getPrompt(personality, 'english');
        final spanishPrompt = PersonalityPrompts.getPrompt(personality, 'spanish');
        
        // Both should be substantial
        expect(englishPrompt.split(' ').length, greaterThan(20));
        expect(spanishPrompt.split(' ').length, greaterThan(20));
        
        // Should be different (translated)
        expect(englishPrompt, isNot(equals(spanishPrompt)));
        
        // Should contain personality-appropriate content
        final personalityName = personality.toString().split('.').last;
        expect(englishPrompt.toLowerCase(), contains(personalityName));
      }
    });

    test('should validate prompt structure and format', () {
      for (final personality in PersonalityMode.values) {
        for (final language in ['english', 'spanish', 'french', 'portuguese', 'italian']) {
          final prompt = PersonalityPrompts.getPrompt(personality, language);
          
          // Should not be empty or just whitespace
          expect(prompt.trim(), isNotEmpty);
          
          // Should not contain placeholder text
          expect(prompt, isNot(contains('[placeholder]')));
          expect(prompt, isNot(contains('TODO')));
          expect(prompt, isNot(contains('FIXME')));
          
          // Should contain instructional language
          final lowerPrompt = prompt.toLowerCase();
          expect(lowerPrompt, anyOf([
            contains('you are'), contains('respond'), contains('be'),
            contains('eres'), contains('responde'), contains('sé'),
            contains('tu es'), contains('réponds'), contains('sois'),
            contains('você é'), contains('responda'), contains('seja'),
            contains('sei'), contains('rispondi'), contains('sii'),
          ]), reason: 'Missing instructional language in $language for $personality');
        }
      }
    });
  });

  group('Prompt Performance and Edge Cases', () {
    test('should handle empty or null inputs gracefully', () {
      expect(() => PersonalityPrompts.buildEmotionalResponsePrompt(
        '', PersonalityMode.toxic, 'english'
      ), returnsNormally);
      
      expect(() => PersonalityPrompts.getPrompt(
        PersonalityMode.toxic, ''
      ), returnsNormally);
    });

    test('should handle unsupported languages gracefully', () {
      final prompt = PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'unsupported_language');
      final englishPrompt = PersonalityPrompts.getPrompt(PersonalityMode.toxic, 'english');
      
      // Should fallback to English
      expect(prompt, equals(englishPrompt));
    });

    test('should handle extreme intensity values', () {
      expect(() => PersonalityPrompts.buildEmotionalResponsePrompt(
        'Test', PersonalityMode.toxic, 'english', emotionalIntensity: -1.0
      ), returnsNormally);
      
      expect(() => PersonalityPrompts.buildEmotionalResponsePrompt(
        'Test', PersonalityMode.toxic, 'english', emotionalIntensity: 2.0
      ), returnsNormally);
    });

    test('should generate prompts efficiently', () {
      final stopwatch = Stopwatch()..start();
      
      // Generate 100 prompts
      for (int i = 0; i < 100; i++) {
        PersonalityPrompts.buildEmotionalResponsePrompt(
          'Test message $i',
          PersonalityMode.values[i % PersonalityMode.values.length],
          'english',
          emotionalIntensity: (i % 10) / 10.0,
        );
      }
      
      stopwatch.stop();
      
      // Should complete within reasonable time (less than 1 second)
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
    });
  });
}