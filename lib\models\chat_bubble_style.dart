import 'package:flutter/material.dart';

/// Enum defining different chat bubble styling options
enum ChatBubbleStyle {
  whatsapp,
  msn,
  telegram,
}

/// Configuration class for chat bubble styling
class ChatBubbleStyleConfig {
  final ChatBubbleStyle style;
  final Color? userBubbleColor;
  final Color? aiBubbleColor;
  final double borderRadius;
  final double tailSize;
  final bool showTail;
  final List<BoxShadow> shadows;
  final EdgeInsets padding;
  final EdgeInsets margin;

  const ChatBubbleStyleConfig({
    required this.style,
    this.userBubbleColor,
    this.aiBubbleColor,
    this.borderRadius = 18.0,
    this.tailSize = 8.0,
    this.showTail = true,
    this.shadows = const [],
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
    this.margin = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
  });

  /// Get predefined style configuration for WhatsApp
  static ChatBubbleStyleConfig whatsapp(ColorScheme colorScheme) {
    return ChatBubbleStyleConfig(
      style: ChatBubbleStyle.whatsapp,
      userBubbleColor: const Color(0xFF128C7E), // WhatsApp green
      aiBubbleColor: colorScheme.surfaceContainerHigh,
      borderRadius: 18.0,
      tailSize: 6.0,
      showTail: true,
      shadows: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 4.0,
          offset: const Offset(0, 2),
        ),
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8.0,
          offset: const Offset(0, 4),
        ),
      ],
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
    );
  }

  /// Get predefined style configuration for MSN
  static ChatBubbleStyleConfig msn(ColorScheme colorScheme) {
    return ChatBubbleStyleConfig(
      style: ChatBubbleStyle.msn,
      userBubbleColor: const Color(0xFF0078D4), // MSN blue
      aiBubbleColor: const Color(0xFFF3F3F3), // Light gray
      borderRadius: 12.0,
      tailSize: 8.0,
      showTail: true,
      shadows: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.15),
          blurRadius: 6.0,
          offset: const Offset(0, 3),
        ),
      ],
      padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 10.0),
      margin: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0),
    );
  }

  /// Get predefined style configuration for Telegram
  static ChatBubbleStyleConfig telegram(ColorScheme colorScheme) {
    return ChatBubbleStyleConfig(
      style: ChatBubbleStyle.telegram,
      userBubbleColor: const Color(0xFF2AABEE), // Telegram blue
      aiBubbleColor: Colors.white,
      borderRadius: 20.0,
      tailSize: 5.0,
      showTail: false, // Telegram doesn't use tails
      shadows: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.08),
          blurRadius: 8.0,
          offset: const Offset(0, 2),
        ),
      ],
      padding: const EdgeInsets.symmetric(horizontal: 18.0, vertical: 14.0),
      margin: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
    );
  }

  /// Get style configuration based on enum value
  static ChatBubbleStyleConfig getConfig(ChatBubbleStyle style, ColorScheme colorScheme) {
    switch (style) {
      case ChatBubbleStyle.whatsapp:
        return whatsapp(colorScheme);
      case ChatBubbleStyle.msn:
        return msn(colorScheme);
      case ChatBubbleStyle.telegram:
        return telegram(colorScheme);
    }
  }
}

/// Enum for message bubble alignment
enum ChatBubbleAlignment {
  left,
  right,
}

/// Enum for message group position
enum MessageGroupPosition {
  single,  // Message is not grouped with any other message
  first,   // First message in a group
  middle,  // Middle message in a group
  last,    // Last message in a group
}

/// Helper class for chat bubble layout calculations
class ChatBubbleLayoutHelper {
  /// Get alignment based on message sender
  static ChatBubbleAlignment getAlignment(bool isUser) {
    return isUser ? ChatBubbleAlignment.right : ChatBubbleAlignment.left;
  }

  /// Get margins for message bubble based on alignment and consecutive status
  static EdgeInsets getMessageMargins(
    ChatBubbleAlignment alignment,
    bool isConsecutive,
    ChatBubbleStyleConfig config,
    double screenWidth,
  ) {
    final baseMargin = config.margin;
    final maxWidth = screenWidth * 0.75; // Max 75% of screen width
    final minMargin = screenWidth * 0.05; // Min 5% margin
    
    switch (alignment) {
      case ChatBubbleAlignment.right:
        return EdgeInsets.only(
          left: maxWidth * 0.3, // Leave space on left
          right: minMargin,
          top: isConsecutive ? baseMargin.top * 0.5 : baseMargin.top,
          bottom: baseMargin.bottom,
        );
      case ChatBubbleAlignment.left:
        return EdgeInsets.only(
          left: minMargin,
          right: maxWidth * 0.3, // Leave space on right
          top: isConsecutive ? baseMargin.top * 0.5 : baseMargin.top,
          bottom: baseMargin.bottom,
        );
    }
  }

  /// Get margins for message bubble based on group position
  static EdgeInsets getMessageMarginsForPosition(
    ChatBubbleAlignment alignment,
    MessageGroupPosition position,
    ChatBubbleStyleConfig config,
    double screenWidth,
  ) {
    final baseMargin = config.margin;
    final maxWidth = screenWidth * 0.75; // Max 75% of screen width
    final minMargin = screenWidth * 0.05; // Min 5% margin
    final groupedTopMargin = baseMargin.top * 0.25; // Very small margin for grouped messages
    final normalTopMargin = baseMargin.top;
    
    double topMargin;
    switch (position) {
      case MessageGroupPosition.single:
        topMargin = normalTopMargin;
        break;
      case MessageGroupPosition.first:
        topMargin = normalTopMargin;
        break;
      case MessageGroupPosition.middle:
        topMargin = groupedTopMargin;
        break;
      case MessageGroupPosition.last:
        topMargin = groupedTopMargin;
        break;
    }
    
    switch (alignment) {
      case ChatBubbleAlignment.right:
        return EdgeInsets.only(
          left: maxWidth * 0.3, // Leave space on left
          right: minMargin,
          top: topMargin,
          bottom: baseMargin.bottom,
        );
      case ChatBubbleAlignment.left:
        return EdgeInsets.only(
          left: minMargin,
          right: maxWidth * 0.3, // Leave space on right
          top: topMargin,
          bottom: baseMargin.bottom,
        );
    }
  }

  /// Get border radius for message bubble based on alignment and consecutive status
  static BorderRadius getBubbleBorderRadius(
    ChatBubbleAlignment alignment,
    bool isConsecutive,
    ChatBubbleStyleConfig config,
  ) {
    final radius = config.borderRadius;
    final tailRadius = config.showTail ? config.tailSize : radius;
    
    switch (alignment) {
      case ChatBubbleAlignment.right:
        return BorderRadius.only(
          topLeft: Radius.circular(radius),
          topRight: Radius.circular(isConsecutive ? radius : tailRadius),
          bottomLeft: Radius.circular(radius),
          bottomRight: Radius.circular(tailRadius),
        );
      case ChatBubbleAlignment.left:
        return BorderRadius.only(
          topLeft: Radius.circular(isConsecutive ? radius : tailRadius),
          topRight: Radius.circular(radius),
          bottomLeft: Radius.circular(tailRadius),
          bottomRight: Radius.circular(radius),
        );
    }
  }

  /// Get border radius for message bubble based on group position
  static BorderRadius getBubbleBorderRadiusForPosition(
    ChatBubbleAlignment alignment,
    MessageGroupPosition position,
    ChatBubbleStyleConfig config,
  ) {
    final radius = config.borderRadius;
    final tailRadius = config.showTail ? config.tailSize : radius;
    final smallRadius = radius * 0.3; // Smaller radius for grouped messages
    
    switch (alignment) {
      case ChatBubbleAlignment.right:
        switch (position) {
          case MessageGroupPosition.single:
            return BorderRadius.only(
              topLeft: Radius.circular(radius),
              topRight: Radius.circular(tailRadius),
              bottomLeft: Radius.circular(radius),
              bottomRight: Radius.circular(tailRadius),
            );
          case MessageGroupPosition.first:
            return BorderRadius.only(
              topLeft: Radius.circular(radius),
              topRight: Radius.circular(tailRadius),
              bottomLeft: Radius.circular(radius),
              bottomRight: Radius.circular(smallRadius),
            );
          case MessageGroupPosition.middle:
            return BorderRadius.only(
              topLeft: Radius.circular(radius),
              topRight: Radius.circular(smallRadius),
              bottomLeft: Radius.circular(radius),
              bottomRight: Radius.circular(smallRadius),
            );
          case MessageGroupPosition.last:
            return BorderRadius.only(
              topLeft: Radius.circular(radius),
              topRight: Radius.circular(smallRadius),
              bottomLeft: Radius.circular(radius),
              bottomRight: Radius.circular(tailRadius),
            );
        }
      case ChatBubbleAlignment.left:
        switch (position) {
          case MessageGroupPosition.single:
            return BorderRadius.only(
              topLeft: Radius.circular(tailRadius),
              topRight: Radius.circular(radius),
              bottomLeft: Radius.circular(tailRadius),
              bottomRight: Radius.circular(radius),
            );
          case MessageGroupPosition.first:
            return BorderRadius.only(
              topLeft: Radius.circular(tailRadius),
              topRight: Radius.circular(radius),
              bottomLeft: Radius.circular(smallRadius),
              bottomRight: Radius.circular(radius),
            );
          case MessageGroupPosition.middle:
            return BorderRadius.only(
              topLeft: Radius.circular(smallRadius),
              topRight: Radius.circular(radius),
              bottomLeft: Radius.circular(smallRadius),
              bottomRight: Radius.circular(radius),
            );
          case MessageGroupPosition.last:
            return BorderRadius.only(
              topLeft: Radius.circular(smallRadius),
              topRight: Radius.circular(radius),
              bottomLeft: Radius.circular(tailRadius),
              bottomRight: Radius.circular(radius),
            );
        }
    }
  }

  /// Get text color based on bubble background
  static Color getTextColor(bool isUser, ChatBubbleStyleConfig config, ColorScheme colorScheme) {
    if (isUser) {
      // For user bubbles, use white text for better contrast
      return Colors.white;
    } else {
      // For AI bubbles, determine based on background color
      final backgroundColor = config.aiBubbleColor ?? colorScheme.surfaceContainerHigh;
      // Use a simple luminance check to determine text color
      return backgroundColor.computeLuminance() > 0.5 
          ? colorScheme.onSurface 
          : Colors.white;
    }
  }
}