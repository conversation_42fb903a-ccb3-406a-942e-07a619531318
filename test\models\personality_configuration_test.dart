import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/models/personality_mode.dart';
import 'package:toxic_chat/models/personality_configuration.dart';

void main() {
  group('PersonalityConfiguration', () {
    test('should create default configuration', () {
      final config = PersonalityConfiguration.defaultConfig();

      expect(config.currentPersonality, equals(PersonalityMode.toxic));
      expect(config.enableEmotionalResponses, isTrue);
      expect(config.emotionalIntensity, equals(0.8));
      expect(config.preferredLanguage, equals('english'));
      expect(config.autoDetectLanguage, isTrue);
    });

    test('should create configuration with custom values', () {
      final config = PersonalityConfiguration(
        currentPersonality: PersonalityMode.witch,
        enableEmotionalResponses: false,
        emotionalIntensity: 0.5,
        preferredLanguage: 'spanish',
        autoDetectLanguage: false,
      );

      expect(config.currentPersonality, equals(PersonalityMode.witch));
      expect(config.enableEmotionalResponses, isFalse);
      expect(config.emotionalIntensity, equals(0.5));
      expect(config.preferredLanguage, equals('spanish'));
      expect(config.autoDetectLanguage, isFalse);
    });

    test('should copy with updated values', () {
      final original = PersonalityConfiguration.defaultConfig();
      final updated = original.copyWith(
        currentPersonality: PersonalityMode.sarcastic,
        emotionalIntensity: 0.6,
      );

      expect(updated.currentPersonality, equals(PersonalityMode.sarcastic));
      expect(updated.emotionalIntensity, equals(0.6));
      expect(
        updated.enableEmotionalResponses,
        equals(original.enableEmotionalResponses),
      );
      expect(updated.preferredLanguage, equals(original.preferredLanguage));
    });

    test('should convert to and from JSON', () {
      final original = PersonalityConfiguration(
        currentPersonality: PersonalityMode.dramatic,
        enableEmotionalResponses: false,
        emotionalIntensity: 0.3,
        preferredLanguage: 'french',
        autoDetectLanguage: false,
        customSettings: {'test': 'value'},
      );

      final json = original.toJson();
      final restored = PersonalityConfiguration.fromJson(json);

      expect(restored.currentPersonality, equals(original.currentPersonality));
      expect(
        restored.enableEmotionalResponses,
        equals(original.enableEmotionalResponses),
      );
      expect(restored.emotionalIntensity, equals(original.emotionalIntensity));
      expect(restored.preferredLanguage, equals(original.preferredLanguage));
      expect(restored.autoDetectLanguage, equals(original.autoDetectLanguage));
      expect(restored.customSettings, equals(original.customSettings));
    });

    test('should handle equality correctly', () {
      final config1 = PersonalityConfiguration(
        currentPersonality: PersonalityMode.toxic,
        emotionalIntensity: 0.8,
      );
      final config2 = PersonalityConfiguration(
        currentPersonality: PersonalityMode.toxic,
        emotionalIntensity: 0.8,
      );
      final config3 = PersonalityConfiguration(
        currentPersonality: PersonalityMode.witch,
        emotionalIntensity: 0.8,
      );

      expect(config1, equals(config2));
      expect(config1, isNot(equals(config3)));
      expect(config1.hashCode, equals(config2.hashCode));
    });

    test('should handle JSON with missing fields gracefully', () {
      final incompleteJson = <String, dynamic>{
        'currentPersonality': 'witch',
        'emotionalIntensity': 0.6,
      };

      final config = PersonalityConfiguration.fromJson(incompleteJson);

      expect(config.currentPersonality, equals(PersonalityMode.witch));
      expect(config.emotionalIntensity, equals(0.6));
      expect(config.enableEmotionalResponses, isTrue); // Default value
      expect(config.preferredLanguage, equals('english')); // Default value
    });
  });
}
