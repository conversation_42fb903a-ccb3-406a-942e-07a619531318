import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';
import 'package:shelf_cors_headers/shelf_cors_headers.dart';
import '../lib/config/config.dart';
import '../lib/handlers/chat_handler.dart';

void main() async {
  // Initialize configuration
  Config.initialize();
  
  // Get configuration
  final port = Config.port;
  final host = Config.host;
  
  // Initialize chat handler
  final chatHandler = ChatHandler();
  
  // Create the handler pipeline
  final handler = Pipeline()
      .addMiddleware(logRequests())
      .addMiddleware(corsHeaders())
      .addMiddleware(_handleCors)
      .addHandler((request) => _router(request, chatHandler));

  // Start the server
  final server = await serve(handler, host, port);
  print('Server running on http://${server.address.host}:${server.port}');
}

// CORS middleware to handle preflight requests
Middleware _handleCors = (Handler handler) {
  return (Request request) async {
    if (request.method == 'OPTIONS') {
      return Response.ok('', headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      });
    }
    
    final response = await handler(request);
    return response.change(headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    });
  };
};

// Basic router
Future<Response> _router(Request request, ChatHandler chatHandler) async {
  final path = request.url.path;
  
  switch (path) {
    case 'health':
      return _handleHealth(request);
    case 'chat':
      return await chatHandler.handleChatRequest(request);
    default:
      return Response.notFound('Route not found');
  }
}

// Health check endpoint
Response _handleHealth(Request request) {
  return Response.ok('{"status": "healthy", "timestamp": "${DateTime.now().toIso8601String()}"}',
      headers: {'Content-Type': 'application/json'});
}