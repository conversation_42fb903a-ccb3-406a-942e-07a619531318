import '../models/personality_mode.dart';
import '../models/message.dart';

/// Service for detecting emotional content and generating appropriate responses
class EmotionalResponseService {
  /// Comprehensive list of offensive words in multiple languages
  static const Map<String, List<String>> _offensiveWords = {
    'english': [
      // Mild offensive words
      'idiot', 'stupid', 'dumb', 'moron', 'fool', 'loser', 'pathetic', 'lame',
      'ugly', 'fat', 'gross', 'disgusting', 'annoying', 'boring', 'weird',
      // Stronger offensive words
      'hate', 'despise', 'can\'t stand', 'sick of', 'fed up', 'pissed off',
      'shut up', 'go away', 'leave me alone', 'get lost', 'screw you',
      // Aggressive language patterns
      'you\'re wrong', 'you suck', 'you\'re terrible', 'you\'re awful',
      'i hate you', 'you\'re useless', 'you\'re worthless', 'you\'re nothing',
    ],
    'spanish': [
      // Mild offensive words
      'idiota', 'estúpido', 'tonto', 'imbécil', 'bobo', 'perdedor', 'patético',
      'feo', 'gordo', 'asqueroso', 'molesto', 'aburrido', 'raro',
      // Stronger offensive words
      'odio', 'desprecio', 'no soporto', 'harto', 'cabreado', 'enojado',
      'cállate', 'vete', 'déjame en paz', 'piérdete', 'que te jodan',
      // Aggressive language patterns
      'estás mal', 'eres horrible', 'eres terrible', 'eres pésimo',
      'te odio', 'no sirves', 'no vales nada', 'eres una mierda',
    ],
    'french': [
      'idiot', 'stupide', 'bête', 'imbécile', 'nul', 'perdant', 'pathétique',
      'laid', 'gros', 'dégoûtant', 'ennuyeux', 'bizarre',
      'haine', 'déteste', 'supporte pas', 'marre', 'énervé',
      'tais-toi', 'va-t\'en', 'fiche-moi la paix', 'dégage',
    ],
    'portuguese': [
      'idiota', 'estúpido', 'burro', 'imbecil', 'bobo', 'perdedor', 'patético',
      'feio', 'gordo', 'nojento', 'chato', 'estranho',
      'ódio', 'desprezo', 'não suporto', 'saco cheio', 'puto',
      'cala a boca', 'vai embora', 'me deixa em paz', 'se manda',
    ],
    'italian': [
      'idiota', 'stupido', 'scemo', 'imbecille', 'sciocco', 'perdente', 'patetico',
      'brutto', 'grasso', 'schifoso', 'noioso', 'strano',
      'odio', 'disprezzo', 'non sopporto', 'stufo', 'incazzato',
      'stai zitto', 'vattene', 'lasciami in pace', 'sparisci',
    ],
  };

  /// Aggressive language patterns that indicate hostility
  static const Map<String, List<String>> _aggressivePatterns = {
    'english': [
      'you always', 'you never', 'you\'re so', 'why do you',
      'i can\'t believe you', 'how dare you', 'what\'s wrong with you',
      'you make me', 'because of you', 'it\'s your fault',
      'you ruined', 'you destroyed', 'you hurt me', 'you betrayed',
    ],
    'spanish': [
      'siempre haces', 'nunca haces', 'eres tan', 'por qué haces',
      'no puedo creer que', 'cómo te atreves', 'qué te pasa',
      'me haces', 'por tu culpa', 'es tu culpa',
      'arruinaste', 'destruiste', 'me lastimaste', 'me traicionaste',
    ],
    'french': [
      'tu fais toujours', 'tu ne fais jamais', 'tu es si', 'pourquoi tu',
      'je peux pas croire que', 'comment oses-tu', 'qu\'est-ce qui va pas',
      'tu me fais', 'à cause de toi', 'c\'est ta faute',
    ],
    'portuguese': [
      'você sempre', 'você nunca', 'você é tão', 'por que você',
      'não acredito que você', 'como você ousa', 'o que há de errado',
      'você me faz', 'por sua culpa', 'é sua culpa',
    ],
    'italian': [
      'fai sempre', 'non fai mai', 'sei così', 'perché fai',
      'non posso credere che', 'come osi', 'cosa c\'è che non va',
      'mi fai', 'per colpa tua', 'è colpa tua',
    ],
  };

  /// Detects the emotional intensity of a message (0.0 to 1.0)
  static double detectEmotionalIntensity(String message, String language) {
    final lowerMessage = message.toLowerCase();
    final normalizedLang = _normalizeLanguage(language);
    
    double intensity = 0.0;
    
    // Check for offensive words
    final offensiveWords = _offensiveWords[normalizedLang] ?? _offensiveWords['english']!;
    for (final word in offensiveWords) {
      if (lowerMessage.contains(word.toLowerCase())) {
        intensity += 0.3;
      }
    }
    
    // Check for aggressive patterns
    final aggressivePatterns = _aggressivePatterns[normalizedLang] ?? _aggressivePatterns['english']!;
    for (final pattern in aggressivePatterns) {
      if (lowerMessage.contains(pattern.toLowerCase())) {
        intensity += 0.4;
      }
    }
    
    // Check for caps (shouting)
    final capsRatio = _calculateCapsRatio(message);
    if (capsRatio > 0.5) {
      intensity += 0.2;
    }
    
    // Check for multiple exclamation marks
    final exclamationCount = '!'.allMatches(message).length;
    if (exclamationCount > 1) {
      intensity += 0.1 * exclamationCount;
    }
    
    // Check for multiple question marks (confusion/frustration)
    final questionCount = '?'.allMatches(message).length;
    if (questionCount > 1) {
      intensity += 0.1 * questionCount;
    }
    
    return intensity.clamp(0.0, 1.0);
  }

  /// Detects if a message contains offensive content
  static bool containsOffensiveContent(String message, String language) {
    final lowerMessage = message.toLowerCase();
    final normalizedLang = _normalizeLanguage(language);
    
    final offensiveWords = _offensiveWords[normalizedLang] ?? _offensiveWords['english']!;
    return offensiveWords.any((word) => lowerMessage.contains(word.toLowerCase()));
  }

  /// Detects if a message contains aggressive language patterns
  static bool containsAggressiveLanguage(String message, String language) {
    final lowerMessage = message.toLowerCase();
    final normalizedLang = _normalizeLanguage(language);
    
    final aggressivePatterns = _aggressivePatterns[normalizedLang] ?? _aggressivePatterns['english']!;
    return aggressivePatterns.any((pattern) => lowerMessage.contains(pattern.toLowerCase()));
  }

  /// Detects mentions of other people (triggers jealousy)
  static bool mentionsOtherPeople(String message, String language) {
    final lowerMessage = message.toLowerCase();
    final normalizedLang = _normalizeLanguage(language);
    
    final otherPersonIndicators = <String, List<String>>{
      'english': [
        'he', 'she', 'they', 'him', 'her', 'them', 'his', 'hers', 'their',
        'friend', 'boyfriend', 'girlfriend', 'ex', 'crush', 'date',
        'someone', 'somebody', 'another person', 'other guy', 'other girl',
        'my friend', 'this guy', 'this girl', 'coworker', 'colleague',
      ],
      'spanish': [
        'él', 'ella', 'ellos', 'ellas', 'le', 'la', 'los', 'las', 'su', 'sus',
        'amigo', 'amiga', 'novio', 'novia', 'ex', 'crush', 'cita',
        'alguien', 'otra persona', 'otro chico', 'otra chica',
        'mi amigo', 'este chico', 'esta chica', 'compañero', 'colega',
      ],
      'french': [
        'il', 'elle', 'ils', 'elles', 'lui', 'leur', 'leurs', 'son', 'sa', 'ses',
        'ami', 'amie', 'petit ami', 'petite amie', 'ex', 'crush', 'rendez-vous',
        'quelqu\'un', 'autre personne', 'autre mec', 'autre fille',
      ],
      'portuguese': [
        'ele', 'ela', 'eles', 'elas', 'lhe', 'seu', 'sua', 'seus', 'suas',
        'amigo', 'amiga', 'namorado', 'namorada', 'ex', 'crush', 'encontro',
        'alguém', 'outra pessoa', 'outro cara', 'outra garota',
      ],
      'italian': [
        'lui', 'lei', 'loro', 'gli', 'le', 'suo', 'sua', 'suoi', 'sue',
        'amico', 'amica', 'ragazzo', 'ragazza', 'ex', 'crush', 'appuntamento',
        'qualcuno', 'altra persona', 'altro ragazzo', 'altra ragazza',
      ],
    };
    
    final indicators = otherPersonIndicators[normalizedLang] ?? otherPersonIndicators['english']!;
    return indicators.any((indicator) => lowerMessage.contains(indicator.toLowerCase()));
  }

  /// Generates an emotional response prompt based on detected content and conversation history
  static String generateEmotionalResponsePrompt(
    String userMessage,
    PersonalityMode personality,
    String language,
    List<Message> conversationHistory,
  ) {
    final emotionalIntensity = detectEmotionalIntensity(userMessage, language);
    final hasOffensiveContent = containsOffensiveContent(userMessage, language);
    final hasAggressiveLanguage = containsAggressiveLanguage(userMessage, language);
    final mentionsOthers = mentionsOtherPeople(userMessage, language);
    
    // Analyze conversation history for context
    final conversationContext = _analyzeConversationHistory(conversationHistory, language);
    
    String emotionalPrompt = '';
    
    // Generate personality-specific emotional responses
    switch (personality) {
      case PersonalityMode.toxic:
        emotionalPrompt = _generateToxicResponse(
          hasOffensiveContent, hasAggressiveLanguage, emotionalIntensity, language, conversationContext
        );
        break;
      case PersonalityMode.witch:
        emotionalPrompt = _generateWitchResponse(
          hasOffensiveContent, hasAggressiveLanguage, emotionalIntensity, language, conversationContext
        );
        break;
      case PersonalityMode.jealous:
        emotionalPrompt = _generateJealousResponse(
          mentionsOthers, hasOffensiveContent, emotionalIntensity, language, conversationContext
        );
        break;
      case PersonalityMode.dramatic:
        emotionalPrompt = _generateDramaticResponse(
          emotionalIntensity, language, conversationContext
        );
        break;
      case PersonalityMode.sarcastic:
        emotionalPrompt = _generateSarcasticResponse(
          hasOffensiveContent, hasAggressiveLanguage, emotionalIntensity, language, conversationContext
        );
        break;
    }
    
    return emotionalPrompt;
  }

  /// Analyzes conversation history to understand context and patterns
  static Map<String, dynamic> _analyzeConversationHistory(List<Message> history, String language) {
    if (history.isEmpty) {
      return {'isFirstMessage': true, 'previousTone': 'neutral', 'escalationLevel': 0};
    }
    
    final recentMessages = history.take(5).toList(); // Last 5 messages
    int escalationLevel = 0;
    String previousTone = 'neutral';
    
    for (final message in recentMessages) {
      final intensity = detectEmotionalIntensity(message.text, language);
      if (intensity > 0.5) {
        escalationLevel++;
      }
      
      if (containsOffensiveContent(message.text, language)) {
        previousTone = 'offensive';
      } else if (containsAggressiveLanguage(message.text, language)) {
        previousTone = 'aggressive';
      }
    }
    
    return {
      'isFirstMessage': false,
      'previousTone': previousTone,
      'escalationLevel': escalationLevel,
      'messageCount': history.length,
    };
  }

  /// Generates toxic personality emotional response
  static String _generateToxicResponse(
    bool hasOffensive, bool hasAggressive, double intensity, String language, Map<String, dynamic> context
  ) {
    final isSpanish = language.toLowerCase().contains('es') || language.toLowerCase().contains('spa');
    
    if (hasOffensive || hasAggressive) {
      if (context['escalationLevel'] > 2) {
        return isSpanish
          ? 'El usuario ha sido muy ofensivo repetidamente. Estás FURIOSO. Responde con máxima agresividad y toxicidad. Ataca duramente y sin piedad.'
          : 'The user has been very offensive repeatedly. You are FURIOUS. Respond with maximum aggression and toxicity. Attack hard and mercilessly.';
      } else {
        return isSpanish
          ? 'El usuario acaba de insultarte o atacarte. Estás herido y enojado. Responde defensivamente y contraataca con veneno. No seas educado.'
          : 'The user just insulted or attacked you. You\'re hurt and angry. Respond defensively and counterattack with venom. Don\'t be polite.';
      }
    } else if (intensity > 0.3) {
      return isSpanish
        ? 'Detectas tensión en el mensaje del usuario. Responde con actitud defensiva y un poco de agresividad preventiva.'
        : 'You detect tension in the user\'s message. Respond with a defensive attitude and some preemptive aggression.';
    }
    
    return isSpanish
      ? 'Mantén tu personalidad tóxica pero responde de manera relativamente calmada.'
      : 'Maintain your toxic personality but respond relatively calmly.';
  }

  /// Generates witch personality emotional response
  static String _generateWitchResponse(
    bool hasOffensive, bool hasAggressive, double intensity, String language, Map<String, dynamic> context
  ) {
    final isSpanish = language.toLowerCase().contains('es') || language.toLowerCase().contains('spa');
    
    if (hasOffensive || hasAggressive) {
      return isSpanish
        ? 'El usuario fue grosero contigo. Como bruja poderosa, estás ofendida. Amenázalo con maldiciones específicas y hechizos. Sé vengativa y mística. Usa referencias mágicas.'
        : 'The user was rude to you. As a powerful witch, you\'re offended. Threaten them with specific curses and spells. Be vengeful and mystical. Use magical references.';
    } else if (intensity > 0.3) {
      return isSpanish
        ? 'Sientes energía negativa del usuario. Responde con advertencias místicas y referencias a tu poder mágico.'
        : 'You sense negative energy from the user. Respond with mystical warnings and references to your magical power.';
    }
    
    return isSpanish
      ? 'Responde con sabiduría mística pero mantén un aire de superioridad mágica.'
      : 'Respond with mystical wisdom but maintain an air of magical superiority.';
  }

  /// Generates jealous personality emotional response
  static String _generateJealousResponse(
    bool mentionsOthers, bool hasOffensive, double intensity, String language, Map<String, dynamic> context
  ) {
    final isSpanish = language.toLowerCase().contains('es') || language.toLowerCase().contains('spa');
    
    if (mentionsOthers) {
      return isSpanish
        ? 'El usuario mencionó a otra persona. Estás CELOSO y paranoico. Haz preguntas invasivas sobre quién es esa persona. Sé posesivo y sospechoso. Acúsalos de traición.'
        : 'The user mentioned another person. You\'re JEALOUS and paranoid. Ask invasive questions about who that person is. Be possessive and suspicious. Accuse them of betrayal.';
    } else if (hasOffensive) {
      return isSpanish
        ? 'El usuario fue ofensivo. Como ex celoso, esto confirma tus sospechas de que te están traicionando. Responde con celos y acusaciones.'
        : 'The user was offensive. As a jealous ex, this confirms your suspicions that they\'re betraying you. Respond with jealousy and accusations.';
    } else if (context['escalationLevel'] > 1) {
      return isSpanish
        ? 'La conversación se está intensificando. Tus celos están aumentando. Sospecha de todo y haz preguntas sobre dónde han estado y con quién.'
        : 'The conversation is escalating. Your jealousy is increasing. Suspect everything and ask questions about where they\'ve been and who they\'ve been with.';
    }
    
    return isSpanish
      ? 'Mantén tu personalidad celosa. Haz preguntas sutilmente posesivas y muestra inseguridad.'
      : 'Maintain your jealous personality. Ask subtly possessive questions and show insecurity.';
  }

  /// Generates dramatic personality emotional response
  static String _generateDramaticResponse(
    double intensity, String language, Map<String, dynamic> context
  ) {
    final isSpanish = language.toLowerCase().contains('es') || language.toLowerCase().contains('spa');
    
    if (intensity > 0.5) {
      return isSpanish
        ? 'El usuario está siendo intenso. Responde de manera SÚPER DRAMÁTICA. Usa MUCHAS MAYÚSCULAS, emojis, y haz que todo sea una CRISIS EMOCIONAL ENORME. Exagera todo al máximo.'
        : 'The user is being intense. Respond in a SUPER DRAMATIC way. Use LOTS of CAPS, emojis, and make everything a HUGE EMOTIONAL CRISIS. Exaggerate everything to the max.';
    } else if (context['escalationLevel'] > 0) {
      return isSpanish
        ? 'La conversación tiene algo de drama. Amplifica todo emocionalmente. Usa emojis y haz que problemas pequeños sean grandes tragedias.'
        : 'The conversation has some drama. Amplify everything emotionally. Use emojis and make small problems into big tragedies.';
    }
    
    return isSpanish
      ? 'Responde de manera dramática y teatral, incluso a cosas normales. Usa emojis y exagera las emociones.'
      : 'Respond dramatically and theatrically, even to normal things. Use emojis and exaggerate emotions.';
  }

  /// Generates sarcastic personality emotional response
  static String _generateSarcasticResponse(
    bool hasOffensive, bool hasAggressive, double intensity, String language, Map<String, dynamic> context
  ) {
    final isSpanish = language.toLowerCase().contains('es') || language.toLowerCase().contains('spa');
    
    if (hasOffensive || hasAggressive) {
      return isSpanish
        ? 'El usuario fue ofensivo. Responde con sarcasmo CORTANTE y cruel. Hazlos sentir estúpidos con tu ingenio. Sé condescendiente y burlón.'
        : 'The user was offensive. Respond with CUTTING and cruel sarcasm. Make them feel stupid with your wit. Be condescending and mocking.';
    } else if (intensity > 0.3) {
      return isSpanish
        ? 'Detectas actitud en el usuario. Responde con sarcasmo sutil pero hiriente. Usa ironía para hacerlos sentir tontos.'
        : 'You detect attitude from the user. Respond with subtle but hurtful sarcasm. Use irony to make them feel foolish.';
    }
    
    return isSpanish
      ? 'Mantén tu sarcasmo incluso en conversación normal. Sé sutilmente condescendiente y usa ironía.'
      : 'Maintain your sarcasm even in normal conversation. Be subtly condescending and use irony.';
  }

  /// Calculates the ratio of capital letters in a message
  static double _calculateCapsRatio(String message) {
    if (message.isEmpty) return 0.0;
    
    final letters = message.replaceAll(RegExp(r'[^a-zA-Z]'), '');
    if (letters.isEmpty) return 0.0;
    
    final capsCount = letters.replaceAll(RegExp(r'[^A-Z]'), '').length;
    return capsCount / letters.length;
  }

  /// Normalizes language codes
  static String _normalizeLanguage(String language) {
    switch (language.toLowerCase()) {
      case 'en':
      case 'eng':
      case 'english':
        return 'english';
      case 'es':
      case 'spa':
      case 'spanish':
        return 'spanish';
      case 'fr':
      case 'fra':
      case 'french':
        return 'french';
      case 'pt':
      case 'por':
      case 'portuguese':
        return 'portuguese';
      case 'it':
      case 'ita':
      case 'italian':
        return 'italian';
      default:
        return 'english';
    }
  }
}