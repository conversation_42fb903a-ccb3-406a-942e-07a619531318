import 'package:flutter_test/flutter_test.dart';
import 'package:toxic_chat/services/emotional_response_service.dart';
import 'package:toxic_chat/models/personality_mode.dart';
import 'package:toxic_chat/models/message.dart';

void main() {
  group('EmotionalResponseService', () {
    group('detectEmotionalIntensity', () {
      test('should detect high intensity for offensive words', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity(
          'You are such an idiot and stupid!',
          'english',
        );
        expect(intensity, greaterThan(0.5));
      });

      test('should detect high intensity for aggressive patterns', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity(
          'You always do this! It\'s your fault!',
          'english',
        );
        expect(intensity, greaterThan(0.5));
      });

      test('should detect intensity from caps (shouting)', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity(
          'WHY ARE YOU DOING THIS TO ME',
          'english',
        );
        expect(intensity, greaterThanOrEqualTo(0.2));
      });

      test('should detect intensity from multiple exclamation marks', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity(
          'This is terrible!!!',
          'english',
        );
        expect(intensity, greaterThan(0.1));
      });

      test('should return low intensity for neutral messages', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity(
          'How are you today?',
          'english',
        );
        expect(intensity, lessThan(0.3));
      });

      test('should detect Spanish offensive words', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity(
          'Eres un idiota y estúpido',
          'spanish',
        );
        expect(intensity, greaterThan(0.5));
      });

      test('should cap intensity at 1.0', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity(
          'YOU ARE THE WORST IDIOT EVER!!!! I HATE YOU SO MUCH!!! YOU ALWAYS RUIN EVERYTHING!!!',
          'english',
        );
        expect(intensity, equals(1.0));
      });
    });

    group('containsOffensiveContent', () {
      test('should detect English offensive words', () {
        expect(
          EmotionalResponseService.containsOffensiveContent('You are an idiot', 'english'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('I hate you', 'english'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('You are pathetic', 'english'),
          isTrue,
        );
      });

      test('should detect Spanish offensive words', () {
        expect(
          EmotionalResponseService.containsOffensiveContent('Eres un idiota', 'spanish'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('Te odio', 'spanish'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('Eres patético', 'spanish'),
          isTrue,
        );
      });

      test('should not detect offensive content in neutral messages', () {
        expect(
          EmotionalResponseService.containsOffensiveContent('How are you?', 'english'),
          isFalse,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('¿Cómo estás?', 'spanish'),
          isFalse,
        );
      });

      test('should be case insensitive', () {
        expect(
          EmotionalResponseService.containsOffensiveContent('You are an IDIOT', 'english'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('ERES UN IDIOTA', 'spanish'),
          isTrue,
        );
      });
    });

    group('containsAggressiveLanguage', () {
      test('should detect English aggressive patterns', () {
        expect(
          EmotionalResponseService.containsAggressiveLanguage('You always do this', 'english'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsAggressiveLanguage('It\'s your fault', 'english'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsAggressiveLanguage('How dare you', 'english'),
          isTrue,
        );
      });

      test('should detect Spanish aggressive patterns', () {
        expect(
          EmotionalResponseService.containsAggressiveLanguage('Siempre haces esto', 'spanish'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsAggressiveLanguage('Es tu culpa', 'spanish'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsAggressiveLanguage('Cómo te atreves', 'spanish'),
          isTrue,
        );
      });

      test('should not detect aggressive language in neutral messages', () {
        expect(
          EmotionalResponseService.containsAggressiveLanguage('What time is it?', 'english'),
          isFalse,
        );
        expect(
          EmotionalResponseService.containsAggressiveLanguage('¿Qué hora es?', 'spanish'),
          isFalse,
        );
      });
    });

    group('mentionsOtherPeople', () {
      test('should detect mentions of other people in English', () {
        expect(
          EmotionalResponseService.mentionsOtherPeople('I was with my friend', 'english'),
          isTrue,
        );
        expect(
          EmotionalResponseService.mentionsOtherPeople('He called me yesterday', 'english'),
          isTrue,
        );
        expect(
          EmotionalResponseService.mentionsOtherPeople('My boyfriend said', 'english'),
          isTrue,
        );
      });

      test('should detect mentions of other people in Spanish', () {
        expect(
          EmotionalResponseService.mentionsOtherPeople('Estuve con mi amigo', 'spanish'),
          isTrue,
        );
        expect(
          EmotionalResponseService.mentionsOtherPeople('Él me llamó ayer', 'spanish'),
          isTrue,
        );
        expect(
          EmotionalResponseService.mentionsOtherPeople('Mi novio dijo', 'spanish'),
          isTrue,
        );
      });

      test('should not detect mentions in messages about self only', () {
        expect(
          EmotionalResponseService.mentionsOtherPeople('I went shopping today', 'english'),
          isFalse,
        );
        expect(
          EmotionalResponseService.mentionsOtherPeople('Fui de compras hoy', 'spanish'),
          isFalse,
        );
      });
    });

    group('generateEmotionalResponsePrompt', () {
      final mockHistory = <Message>[
        Message(
          text: 'Hello',
          isUser: true,
          timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        ),
        Message(
          text: 'Hi there',
          isUser: false,
          timestamp: DateTime.now().subtract(const Duration(minutes: 4)),
        ),
      ];

      test('should generate toxic response for offensive content', () {
        final prompt = EmotionalResponseService.generateEmotionalResponsePrompt(
          'You are such an idiot!',
          PersonalityMode.toxic,
          'english',
          mockHistory,
        );
        
        expect(prompt.toLowerCase(), contains('hurt'));
        expect(prompt.toLowerCase(), contains('angry'));
        expect(prompt.toLowerCase(), contains('attack'));
      });

      test('should generate witch response for offensive content', () {
        final prompt = EmotionalResponseService.generateEmotionalResponsePrompt(
          'You are so stupid!',
          PersonalityMode.witch,
          'english',
          mockHistory,
        );
        
        expect(prompt.toLowerCase(), contains('curse'));
        expect(prompt.toLowerCase(), contains('spell'));
        expect(prompt.toLowerCase(), contains('mystical'));
      });

      test('should generate jealous response for mentions of others', () {
        final prompt = EmotionalResponseService.generateEmotionalResponsePrompt(
          'I was talking to my friend today',
          PersonalityMode.jealous,
          'english',
          mockHistory,
        );
        
        expect(prompt.toLowerCase(), contains('jealous'));
        expect(prompt.toLowerCase(), contains('possessive'));
        expect(prompt.toLowerCase(), contains('who'));
      });

      test('should generate dramatic response with high intensity', () {
        final prompt = EmotionalResponseService.generateEmotionalResponsePrompt(
          'THIS IS TERRIBLE!!!',
          PersonalityMode.dramatic,
          'english',
          mockHistory,
        );
        
        expect(prompt.toLowerCase(), contains('dramatic'));
        expect(prompt.toLowerCase(), contains('exaggerate'));
        expect(prompt.toLowerCase(), contains('emojis'));
      });

      test('should generate sarcastic response for offensive content', () {
        final prompt = EmotionalResponseService.generateEmotionalResponsePrompt(
          'You don\'t know anything!',
          PersonalityMode.sarcastic,
          'english',
          mockHistory,
        );
        
        expect(prompt.toLowerCase(), contains('sarcasm'));
        expect(prompt.toLowerCase(), contains('condescending'));
        expect(prompt.toLowerCase(), contains('irony'));
      });

      test('should handle Spanish language responses', () {
        final prompt = EmotionalResponseService.generateEmotionalResponsePrompt(
          'Eres un idiota',
          PersonalityMode.toxic,
          'spanish',
          mockHistory,
        );
        
        expect(prompt, contains('herido'));
        expect(prompt, contains('enojado'));
      });

      test('should consider conversation history for escalation', () {
        final escalatedHistory = <Message>[
          Message(
            text: 'You are stupid!',
            isUser: true,
            timestamp: DateTime.now().subtract(const Duration(minutes: 10)),
          ),
          Message(
            text: 'Don\'t call me that!',
            isUser: false,
            timestamp: DateTime.now().subtract(const Duration(minutes: 9)),
          ),
          Message(
            text: 'You are pathetic!',
            isUser: true,
            timestamp: DateTime.now().subtract(const Duration(minutes: 8)),
          ),
          Message(
            text: 'Stop being mean!',
            isUser: false,
            timestamp: DateTime.now().subtract(const Duration(minutes: 7)),
          ),
        ];

        final prompt = EmotionalResponseService.generateEmotionalResponsePrompt(
          'I hate you!',
          PersonalityMode.toxic,
          'english',
          escalatedHistory,
        );
        
        // Should generate a more intense response due to escalation
        expect(prompt.toLowerCase(), anyOf([
          contains('repeatedly'),
          contains('furious'),
          contains('maximum'),
          contains('hurt'),
          contains('angry'),
        ]));
      });

      test('should handle empty conversation history', () {
        final prompt = EmotionalResponseService.generateEmotionalResponsePrompt(
          'Hello',
          PersonalityMode.toxic,
          'english',
          [],
        );
        
        expect(prompt, isNotEmpty);
        expect(prompt.toLowerCase(), contains('calm'));
      });
    });

    group('language support', () {
      test('should support multiple languages for offensive content detection', () {
        // Test with language-specific offensive words
        expect(
          EmotionalResponseService.containsOffensiveContent('idiot', 'english'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('idiota', 'spanish'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('idiot', 'french'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('idiota', 'portuguese'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('idiota', 'italian'),
          isTrue,
        );
      });

      test('should normalize language codes correctly', () {
        // Test English variations
        expect(
          EmotionalResponseService.containsOffensiveContent('idiot', 'en'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('idiot', 'eng'),
          isTrue,
        );
        
        // Test Spanish variations
        expect(
          EmotionalResponseService.containsOffensiveContent('idiota', 'es'),
          isTrue,
        );
        expect(
          EmotionalResponseService.containsOffensiveContent('idiota', 'spa'),
          isTrue,
        );
      });

      test('should fallback to English for unsupported languages', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity(
          'You are an idiot',
          'unsupported_language',
        );
        expect(intensity, greaterThan(0.0));
      });
    });

    group('edge cases', () {
      test('should handle empty messages', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity('', 'english');
        expect(intensity, equals(0.0));
        
        final hasOffensive = EmotionalResponseService.containsOffensiveContent('', 'english');
        expect(hasOffensive, isFalse);
      });

      test('should handle messages with only punctuation', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity('!!!???', 'english');
        expect(intensity, greaterThan(0.0));
      });

      test('should handle mixed case messages', () {
        final hasOffensive = EmotionalResponseService.containsOffensiveContent(
          'YoU aRe An IdIoT',
          'english',
        );
        expect(hasOffensive, isTrue);
      });

      test('should handle messages with special characters', () {
        final intensity = EmotionalResponseService.detectEmotionalIntensity(
          'You are an @#\$% idiot!!!',
          'english',
        );
        expect(intensity, greaterThan(0.5));
      });
    });
  });
}