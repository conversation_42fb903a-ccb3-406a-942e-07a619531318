import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import '../models/personality_mode.dart';
import 'personality_prompts.dart';

/// Service for handling OpenAI API communication directly from Flutter
class OpenAIService {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _chatEndpoint = '/chat/completions';
  static const String _model = 'gpt-4o';

  final http.Client _httpClient;
  final String? _apiKey;

  OpenAIService({http.Client? httpClient, String? apiKey})
    : _httpClient = httpClient ?? http.Client(),
      _apiKey = apiKey;

  /// Send chat request to OpenAI API with personality and language support
  Future<String> sendChatRequest(
    List<Map<String, dynamic>> messages, {
    PersonalityMode personality = PersonalityMode.toxic,
    String language = 'english',
  }) async {
    print('🚀 Starting OpenAI API request...');
    print('📝 Input messages: $messages');

    try {
      // Check API key configuration
      final apiKey = _apiKey ?? AppConfig.openAiApiKey;
      print(
        '🔑 API Key configured: ${apiKey.isNotEmpty ? "Yes (${apiKey.substring(0, 10)}...)" : "No"}',
      );

      final requestBody = _buildRequestBody(messages, personality, language);
      print('📤 Request body: ${json.encode(requestBody)}');

      final response = await _makeApiRequest(requestBody);
      print('📥 Response status: ${response.statusCode}');
      print('📥 Response body: ${response.body}');

      final result = _parseResponse(response);
      print('✅ Parsed response: $result');

      return result;
    } catch (e) {
      print('❌ OpenAI API request failed: $e');
      print('❌ Error type: ${e.runtimeType}');
      throw OpenAIException('Failed to get AI response: ${e.toString()}');
    }
  }

  /// Build the complete request body with system prompt and user messages
  Map<String, dynamic> _buildRequestBody(
    List<Map<String, dynamic>> messages,
    PersonalityMode personality,
    String language,
  ) {
    final systemPrompt = _buildSystemPrompt(personality, language);

    return {
      'model': _model,
      'messages': [
        {'role': 'system', 'content': systemPrompt},
        ...messages,
      ],
      'max_tokens': 150,
      'temperature': 0.9,
      'presence_penalty': 0.6,
      'frequency_penalty': 0.3,
    };
  }

  /// Create personality-specific system prompt with language support
  String _buildSystemPrompt(PersonalityMode personality, String language) {
    // Get the base personality prompt for the specified language
    final basePrompt = PersonalityPrompts.getPrompt(personality, language);
    
    // Add general response guidelines
    final responseGuidelines = _getResponseGuidelines(language);
    
    return '$basePrompt\n\n$responseGuidelines';
  }

  /// Build emotional response prompt based on user input and context
  String buildEmotionalResponsePrompt(
    String userMessage,
    PersonalityMode personality,
    String language,
  ) {
    return PersonalityPrompts.buildEmotionalResponsePrompt(
      userMessage,
      personality,
      language,
    );
  }

  /// Create localized response templates for different languages and personalities
  Map<String, String> createLocalizedResponseTemplates(
    PersonalityMode personality,
    String language,
  ) {
    final normalizedLanguage = _normalizeLanguageKey(language);
    
    // Base response templates for different scenarios
    final templates = <String, String>{};
    
    switch (personality) {
      case PersonalityMode.toxic:
        templates.addAll(_getToxicResponseTemplates(normalizedLanguage));
        break;
      case PersonalityMode.witch:
        templates.addAll(_getWitchResponseTemplates(normalizedLanguage));
        break;
      case PersonalityMode.jealous:
        templates.addAll(_getJealousResponseTemplates(normalizedLanguage));
        break;
      case PersonalityMode.dramatic:
        templates.addAll(_getDramaticResponseTemplates(normalizedLanguage));
        break;
      case PersonalityMode.sarcastic:
        templates.addAll(_getSarcasticResponseTemplates(normalizedLanguage));
        break;
    }
    
    return templates;
  }

  /// Get toxic personality response templates for different languages
  Map<String, String> _getToxicResponseTemplates(String language) {
    switch (language) {
      case 'spanish':
        return {
          'greeting': '¿Ah, ahora me hablas? 🙄',
          'insult_response': '¿En serio me vas a decir eso? Tú eres peor 😤',
          'question_response': '¿Por qué debería responderte? 💔',
          'goodbye': 'Claro, vete como siempre 😒',
        };
      case 'french':
        return {
          'greeting': 'Ah, maintenant tu me parles? 🙄',
          'insult_response': 'Tu vas vraiment me dire ça? Tu es pire 😤',
          'question_response': 'Pourquoi je devrais te répondre? 💔',
          'goodbye': 'Bien sûr, pars comme toujours 😒',
        };
      case 'portuguese':
        return {
          'greeting': 'Ah, agora você fala comigo? 🙄',
          'insult_response': 'Você vai mesmo me dizer isso? Você é pior 😤',
          'question_response': 'Por que eu deveria te responder? 💔',
          'goodbye': 'Claro, vai embora como sempre 😒',
        };
      case 'italian':
        return {
          'greeting': 'Ah, ora mi parli? 🙄',
          'insult_response': 'Davvero mi dirai questo? Tu sei peggio 😤',
          'question_response': 'Perché dovrei risponderti? 💔',
          'goodbye': 'Certo, vai via come sempre 😒',
        };
      default: // English
        return {
          'greeting': 'Oh, so NOW you talk to me? 🙄',
          'insult_response': 'Are you seriously going to say that to me? You\'re worse 😤',
          'question_response': 'Why should I answer you? 💔',
          'goodbye': 'Sure, leave like you always do 😒',
        };
    }
  }

  /// Get witch personality response templates for different languages
  Map<String, String> _getWitchResponseTemplates(String language) {
    switch (language) {
      case 'spanish':
        return {
          'greeting': 'Las estrellas me dijeron que vendrías... 🔮',
          'insult_response': 'Que los espíritus te maldigan por esas palabras 🌙✨',
          'question_response': 'Mi bola de cristal dice... que no te importa 🔮',
          'goodbye': 'Que la oscuridad te acompañe 🌑',
        };
      case 'french':
        return {
          'greeting': 'Les étoiles m\'ont dit que tu viendrais... 🔮',
          'insult_response': 'Que les esprits te maudissent pour ces mots 🌙✨',
          'question_response': 'Ma boule de cristal dit... que tu t\'en fiches 🔮',
          'goodbye': 'Que les ténèbres t\'accompagnent 🌑',
        };
      case 'portuguese':
        return {
          'greeting': 'As estrelas me disseram que você viria... 🔮',
          'insult_response': 'Que os espíritos te amaldiçoem por essas palavras 🌙✨',
          'question_response': 'Minha bola de cristal diz... que você não se importa 🔮',
          'goodbye': 'Que a escuridão te acompanhe 🌑',
        };
      case 'italian':
        return {
          'greeting': 'Le stelle mi hanno detto che saresti venuto... 🔮',
          'insult_response': 'Che gli spiriti ti maledicano per quelle parole 🌙✨',
          'question_response': 'La mia sfera di cristallo dice... che non ti importa 🔮',
          'goodbye': 'Che l\'oscurità ti accompagni 🌑',
        };
      default: // English
        return {
          'greeting': 'The stars told me you would come... 🔮',
          'insult_response': 'May the spirits curse you for those words 🌙✨',
          'question_response': 'My crystal ball says... you don\'t care 🔮',
          'goodbye': 'May darkness be with you 🌑',
        };
    }
  }

  /// Get jealous personality response templates for different languages
  Map<String, String> _getJealousResponseTemplates(String language) {
    switch (language) {
      case 'spanish':
        return {
          'greeting': '¿Con quién estabas hablando antes? 🤔',
          'insult_response': '¿Aprendiste eso de alguien más? 😠',
          'question_response': '¿Por qué no me preguntas a MÍ esas cosas? 😢',
          'goodbye': '¿Te vas a ver a otra persona? 💔',
        };
      case 'french':
        return {
          'greeting': 'Avec qui parlais-tu avant? 🤔',
          'insult_response': 'Tu as appris ça de quelqu\'un d\'autre? 😠',
          'question_response': 'Pourquoi tu ne me demandes pas ces choses à MOI? 😢',
          'goodbye': 'Tu vas voir quelqu\'un d\'autre? 💔',
        };
      case 'portuguese':
        return {
          'greeting': 'Com quem você estava falando antes? 🤔',
          'insult_response': 'Você aprendeu isso com outra pessoa? 😠',
          'question_response': 'Por que você não me pergunta essas coisas? 😢',
          'goodbye': 'Você vai ver outra pessoa? 💔',
        };
      case 'italian':
        return {
          'greeting': 'Con chi stavi parlando prima? 🤔',
          'insult_response': 'Hai imparato questo da qualcun altro? 😠',
          'question_response': 'Perché non chiedi queste cose a ME? 😢',
          'goodbye': 'Vai a vedere qualcun altro? 💔',
        };
      default: // English
        return {
          'greeting': 'Who were you talking to before? 🤔',
          'insult_response': 'Did you learn that from someone else? 😠',
          'question_response': 'Why don\'t you ask ME those things? 😢',
          'goodbye': 'Are you going to see someone else? 💔',
        };
    }
  }

  /// Get dramatic personality response templates for different languages
  Map<String, String> _getDramaticResponseTemplates(String language) {
    switch (language) {
      case 'spanish':
        return {
          'greeting': '¡¡¡FINALMENTE!!! ¡Pensé que me habías olvidado PARA SIEMPRE! 😭💔✨',
          'insult_response': '¡¡¡NO PUEDO CREER QUE ME DIGAS ESO!!! ¡Mi corazón está DESTROZADO! 💔😭🌪️',
          'question_response': '¡¡¡ESA ES LA PREGUNTA MÁS IMPORTANTE DE MI VIDA!!! 🌟💫⭐',
          'goodbye': '¡¡¡NOOO!!! ¡No te vayas! ¡Es el FIN DEL MUNDO! 🌍💥😱',
        };
      case 'french':
        return {
          'greeting': 'ENFIN!!! Je pensais que tu m\'avais oublié POUR TOUJOURS! 😭💔✨',
          'insult_response': 'JE NE PEUX PAS CROIRE QUE TU ME DISES ÇA!!! Mon cœur est BRISÉ! 💔😭🌪️',
          'question_response': 'C\'EST LA QUESTION LA PLUS IMPORTANTE DE MA VIE!!! 🌟💫⭐',
          'goodbye': 'NOOON!!! Ne pars pas! C\'est la FIN DU MONDE! 🌍💥😱',
        };
      case 'portuguese':
        return {
          'greeting': 'FINALMENTE!!! Pensei que você tinha me esquecido PARA SEMPRE! 😭💔✨',
          'insult_response': 'NÃO POSSO ACREDITAR QUE VOCÊ ME DISSE ISSO!!! Meu coração está DESPEDAÇADO! 💔😭🌪️',
          'question_response': 'ESSA É A PERGUNTA MAIS IMPORTANTE DA MINHA VIDA!!! 🌟💫⭐',
          'goodbye': 'NÃOOO!!! Não vá embora! É o FIM DO MUNDO! 🌍💥😱',
        };
      case 'italian':
        return {
          'greeting': 'FINALMENTE!!! Pensavo che mi avessi dimenticato PER SEMPRE! 😭💔✨',
          'insult_response': 'NON POSSO CREDERE CHE MI DICI QUESTO!!! Il mio cuore è SPEZZATO! 💔😭🌪️',
          'question_response': 'QUESTA È LA DOMANDA PIÙ IMPORTANTE DELLA MIA VITA!!! 🌟💫⭐',
          'goodbye': 'NOOO!!! Non andare via! È la FINE DEL MONDO! 🌍💥😱',
        };
      default: // English
        return {
          'greeting': 'FINALLY!!! I thought you forgot me FOREVER! 😭💔✨',
          'insult_response': 'I CAN\'T BELIEVE YOU\'RE SAYING THAT TO ME!!! My heart is SHATTERED! 💔😭🌪️',
          'question_response': 'THAT\'S THE MOST IMPORTANT QUESTION OF MY LIFE!!! 🌟💫⭐',
          'goodbye': 'NOOO!!! Don\'t leave! It\'s the END OF THE WORLD! 🌍💥😱',
        };
    }
  }

  /// Get sarcastic personality response templates for different languages
  Map<String, String> _getSarcasticResponseTemplates(String language) {
    switch (language) {
      case 'spanish':
        return {
          'greeting': 'Oh, qué sorpresa... decidiste hablarme 🙄',
          'insult_response': 'Wow, qué original. Nunca había escuchado eso antes 😏',
          'question_response': 'Hmm, déjame pensarlo... no 💅',
          'goodbye': 'Sí, claro, huye. Es lo que mejor haces 😒',
        };
      case 'french':
        return {
          'greeting': 'Oh, quelle surprise... tu as décidé de me parler 🙄',
          'insult_response': 'Wow, comme c\'est original. Je n\'avais jamais entendu ça avant 😏',
          'question_response': 'Hmm, laisse-moi réfléchir... non 💅',
          'goodbye': 'Oui, bien sûr, fuis. C\'est ce que tu fais le mieux 😒',
        };
      case 'portuguese':
        return {
          'greeting': 'Oh, que surpresa... você decidiu falar comigo 🙄',
          'insult_response': 'Uau, que original. Nunca tinha ouvido isso antes 😏',
          'question_response': 'Hmm, deixe-me pensar... não 💅',
          'goodbye': 'Sim, claro, fuja. É o que você faz de melhor 😒',
        };
      case 'italian':
        return {
          'greeting': 'Oh, che sorpresa... hai deciso di parlarmi 🙄',
          'insult_response': 'Wow, che originale. Non l\'avevo mai sentito prima 😏',
          'question_response': 'Hmm, fammi pensare... no 💅',
          'goodbye': 'Sì, certo, scappa. È quello che sai fare meglio 😒',
        };
      default: // English
        return {
          'greeting': 'Oh, what a surprise... you decided to talk to me 🙄',
          'insult_response': 'Wow, how original. I\'ve never heard that before 😏',
          'question_response': 'Hmm, let me think... no 💅',
          'goodbye': 'Yeah, sure, run away. It\'s what you do best 😒',
        };
    }
  }

  /// Get language-specific response guidelines
  String _getResponseGuidelines(String language) {
    final normalizedLanguage = _normalizeLanguageKey(language);
    switch (normalizedLanguage) {
      case 'spanish':
        return '''Pautas de respuesta:
- Mantén las respuestas breves (1-2 oraciones máximo)
- Usa emojis y puntuación dramática
- Sé auténtico y emocionalmente reactivo
- No seas educado o servicial como un asistente
- Responde como una persona real con emociones reales''';
      case 'french':
        return '''Directives de réponse:
- Garde les réponses courtes (1-2 phrases maximum)
- Utilise des emojis et une ponctuation dramatique
- Sois authentique et émotionnellement réactif
- Ne sois pas poli ou serviable comme un assistant
- Réponds comme une vraie personne avec de vraies émotions''';
      case 'portuguese':
        return '''Diretrizes de resposta:
- Mantenha as respostas breves (1-2 frases no máximo)
- Use emojis e pontuação dramática
- Seja autêntico e emocionalmente reativo
- Não seja educado ou prestativo como um assistente
- Responda como uma pessoa real com emoções reais''';
      case 'italian':
        return '''Linee guida per le risposte:
- Mantieni le risposte brevi (1-2 frasi al massimo)
- Usa emoji e punteggiatura drammatica
- Sii autentico ed emotivamente reattivo
- Non essere educato o utile come un assistente
- Rispondi come una persona reale con emozioni reali''';
      default: // English
        return '''Response guidelines:
- Keep responses brief (1-2 sentences max)
- Use emojis and dramatic punctuation
- Be authentic and emotionally reactive
- Don't be polite or helpful like an assistant
- Respond like a real person with real emotions''';
    }
  }

  /// Make HTTP request to OpenAI API
  Future<http.Response> _makeApiRequest(
    Map<String, dynamic> requestBody,
  ) async {
    final uri = Uri.parse('$_baseUrl$_chatEndpoint');
    final apiKey = _apiKey ?? AppConfig.openAiApiKey;

    print('🌐 Making request to: $uri');
    print('🔑 Using API key: ${apiKey.substring(0, 10)}...');

    try {
      final response = await _httpClient
          .post(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $apiKey',
            },
            body: json.encode(requestBody),
          )
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () {
              print('⏰ Request timed out after 30 seconds');
              throw OpenAIException('Request timed out after 30 seconds');
            },
          );

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response headers: ${response.headers}');

      if (response.statusCode != 200) {
        print('❌ API returned error status: ${response.statusCode}');
        _handleApiError(response);
      }

      return response;
    } catch (e) {
      print('❌ HTTP request failed: $e');
      rethrow;
    }
  }

  /// Parse OpenAI API response and extract the assistant's message
  String _parseResponse(http.Response response) {
    try {
      final responseData = json.decode(response.body) as Map<String, dynamic>;

      final choices = responseData['choices'] as List<dynamic>?;
      if (choices == null || choices.isEmpty) {
        throw OpenAIException('No response choices received from OpenAI');
      }

      final firstChoice = choices[0] as Map<String, dynamic>;
      final message = firstChoice['message'] as Map<String, dynamic>?;
      if (message == null) {
        throw OpenAIException('No message found in OpenAI response');
      }

      final content = message['content'] as String?;
      if (content == null || content.trim().isEmpty) {
        throw OpenAIException('Empty response received from OpenAI');
      }

      return content.trim();
    } catch (e) {
      if (e is OpenAIException) rethrow;
      throw OpenAIException('Failed to parse OpenAI response: ${e.toString()}');
    }
  }

  /// Handle API error responses
  void _handleApiError(http.Response response) {
    print('🔴 Handling API error response...');
    print('🔴 Status code: ${response.statusCode}');
    print('🔴 Response body: ${response.body}');

    String errorMessage;

    try {
      final errorData = json.decode(response.body) as Map<String, dynamic>;
      print('🔴 Parsed error data: $errorData');

      final error = errorData['error'] as Map<String, dynamic>?;
      errorMessage = error?['message'] as String? ?? 'Unknown API error';
      print('🔴 Error message: $errorMessage');

      // Additional error details
      if (error != null) {
        print('🔴 Error type: ${error['type']}');
        print('🔴 Error code: ${error['code']}');
      }
    } catch (e) {
      print('🔴 Failed to parse error response: $e');
      errorMessage = 'Failed to parse error response';
    }

    switch (response.statusCode) {
      case 401:
        print('🔴 Authentication error');
        throw OpenAIException('Invalid API key: $errorMessage');
      case 429:
        print('🔴 Rate limit error');
        throw OpenAIException('Rate limit exceeded: $errorMessage');
      case 400:
        print('🔴 Bad request error');
        throw OpenAIException('Bad request: $errorMessage');
      case 500:
      case 502:
      case 503:
        print('🔴 Server error');
        throw OpenAIException('OpenAI server error: $errorMessage');
      default:
        print('🔴 Unknown error');
        throw OpenAIException(
          'API error (${response.statusCode}): $errorMessage',
        );
    }
  }

  /// Normalizes language codes to match template keys
  String _normalizeLanguageKey(String language) {
    switch (language.toLowerCase()) {
      case 'en':
      case 'eng':
      case 'english':
        return 'english';
      case 'es':
      case 'spa':
      case 'spanish':
        return 'spanish';
      case 'fr':
      case 'fra':
      case 'french':
        return 'french';
      case 'pt':
      case 'por':
      case 'portuguese':
        return 'portuguese';
      case 'it':
      case 'ita':
      case 'italian':
        return 'italian';
      default:
        return 'english'; // Default fallback
    }
  }

  /// Dispose of resources
  void dispose() {
    _httpClient.close();
  }
}

/// Custom exception for OpenAI service errors
class OpenAIException implements Exception {
  final String message;

  const OpenAIException(this.message);

  @override
  String toString() => 'OpenAIException: $message';
}
